//+------------------------------------------------------------------+
//|                 Innovationx Range Master Pro.mq4                  |
//|      Professional Range Breakout Trading System for MT4          |
//|      Designed by <PERSON>                          |
//|      Contact: +49 1521 6294394                                   |
//|      Copyright 2025, Innovationx International                   |
//|      https://innovationxinternational.com                        |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Innovationx International"
#property link      "https://innovationxinternational.com"
#property version   "2.0"
#property strict
#property description "Professional Range Breakout Trading System"

// ================================
// Trade Sizing & Risk Management
// ================================
input string SECTION_1 = "--- Trade Sizing & Risk Management ---\nConfigure how much to trade, risk per trade, and position sizing method.";
input double LotSize = 0.01;                // Default trading lot size for all trades unless overridden.
input double InitialTradeRiskPercent = 0;   // % of balance to risk on the first trade (0 = use min lot).
input bool UseFixedRisk = false;            // Use a fixed money risk per trade (true/false).
input double RiskAmount = 17.0;             // Amount of money to risk per trade (if fixed risk).
input double RiskPercent = 1.0;             // Percentage risk per trade (if using percent risk).
input bool UsePercentRisk = false;          // Use percent risk instead of fixed amount (true/false).
input double PointValue = 0.20;             // Value of 1 point in account currency.
input int MaxTrades = 1;                    // Max trades per direction (open at any time).
input int MaxDailyTradesCount = 0;          // Max total trades per day (0 = unlimited).

// ================================
// Profit Targets
// ================================
input string SECTION_2 = "--- Profit Targets ---\nSet your take profit rules for each trade.";
input double TradeTargetR = 1.5;            // Take profit as a multiple of stop loss (R-multiple).
input double TradeTargetPoints = 0;         // Take profit in points (overrides R if >0).

// ================================
// Stop Loss & Protection
// ================================
input string SECTION_3 = "--- Stop Loss & Protection ---\nConfigure stop loss rules and additional protection.";
enum StopLossType { SL_RANGE, SL_POINTS, SL_PRICE, SL_MIDRANGE };
input StopLossType StopLossMethod = SL_RANGE; // Method for setting stop loss.
input bool UseRangeStopLoss = true;         // Use range boundaries as stop loss (true/false). (Deprecated: Use StopLossMethod)
input int AddedStopLoss = 0;                // Additional points for stop loss (used with SL_RANGE and SL_MIDRANGE).
input int FixedStopLossPoints = 0;          // Fixed stop loss distance in points (used with SL_POINTS).
input double FixedStopLossPrice = 0.0;      // Fixed stop loss price level (used with SL_PRICE).
input bool ScalpingMode = false;            // Enable aggressive scalping mode (25% loss reduction + hawk trailing).

// ================================
// Breakeven Logic
// ================================
input string SECTION_4 = "--- Breakeven Logic ---\nConfigure when and how to move stops to breakeven.";
input bool UseBreakEven = true;             // Move to breakeven when in profit (true/false).
input int BreakEvenPoints = 300;            // Points before breakeven (default 300).
input bool UseBreakEvenPercent = false;     // Use percentage for breakeven trigger (true/false).
input double BreakEvenPercent = 0.0;        // Percentage profit before breakeven triggers (e.g., 1.0 for 1%).
input int BETriggerPoints = 4500;           // Points before breakeven triggers (45 pips default for normal mode).
input double BETriggerPercent = 0;          // (Alternative) % profit before breakeven triggers: set only one.

// ================================
// Trailing Stop Management
// ================================
input string SECTION_5 = "--- Trailing Stop Management ---\nConfigure trailing stop rules to lock in profits.";
input bool UseTrailingStop = true;          // Use trailing stop (true/false).
input int TrailingStopPoints = 200;         // Points for trailing stop distance (default 200).
input bool UseTrailingStopPercent = false;  // Use percentage for trailing stop distance (true/false).
input double TrailingStopPercent = 0.0;     // Percentage distance for trailing stop (e.g., 1.0 for 1%).
// (Add any other trailing stop parameters here if present)

// ================================
// Range & Session Timing
// ================================
// Trading Session Enum
enum ENUM_TRADING_SESSION
{
   SESSION_24HR = 0,     // 24hr (06:00)
   SESSION_LONDON = 1,   // London (08:00)
   SESSION_US = 2        // US (14:00)
};
// Range mode dropdown
enum ENUM_RANGE_MODE
{
   RANGE_STANDARD_0000_0600 = 0,      // Standard: 00:00–06:00
   RANGE_DIFFERENT_2345_0600 = 1      // Different: 23:45–06:00 (includes last 15 minutes before midnight)
};

input string SECTION_6 = "--- Range & Session Timing ---\nDefine when the range is measured and which market session to trade.";
input int RangeStartHour = 0;              // When the range starts (hour, 0 = 00:00 server).
input int RangeStartMinute = 0;             // When the range starts (minute).
input int RangeEndHour = 6;                // When the range ends (hour, e.g., 6 for 06:00).
input int RangeEndMinute = 0;               // When the range ends (minute).
input bool UseServerTimeForRange = true;   // Build range using broker server time only.

input ENUM_RANGE_MODE RangeMode = RANGE_STANDARD_0000_0600; // Choose range mode (dropdown)
input ENUM_TRADING_SESSION TradingSessionPreset = SESSION_24HR; // Trading Session
input int CustomSessionStartHour = 0;       // Custom session start hour.
input int CustomSessionEndHour = 23;        // Custom session end hour.



// ================================
// Entry Buffer & Filtering
// ================================
input string SECTION_7 = "--- Entry Buffer & Filtering ---\nFine-tune entry buffer, minimum lot logic, and basic entry filters.";
input bool EnableDynamicEntryBuffer = false; // Use dynamic entry buffer (true/false).
input int EntryBufferPoints = 20;           // Entry buffer in points.
input double EntryBufferPercent = 0.1;      // Entry buffer as percent of range.
input bool AlwaysUseMinLot = false;         // Always use broker minimum lot size (true/false).

// ================================
// Multi-Trade & Per-Trade Settings
// ================================
input string SECTION_8 = "--- Multi-Trade & Per-Trade Settings ---\nConfigure the number of trades per breakout and per-trade parameters.";
input int NumBreakoutTrades = 1;            // Number of breakout trades per side.

// -- Trade 1 --
input double LotSize1 = 0.01;               // Lot size for Trade 1.
input double TP1 = 1000;                    // Take profit for Trade 1 (default 1000 points).
input int SL1 = 0;                          // Stop loss offset for Trade 1 (points, 0 = default).
input int Trade1Rule = 0;                   // 0=Normal, 1=TightSL, 2=BreakevenRunner.
input string Comment1 = "Trade 1";
input color Trade1Color = clrGreen;
// -- Trade 2 --
input double LotSize2 = 0.01;               // Lot size for Trade 2.
input double TP2 = 1000;                    // Take profit for Trade 2 (default 1000 points).
input int SL2 = 0;
input int Trade2Rule = 0;
input string Comment2 = "Trade 2";
input color Trade2Color = clrBlue;
// -- Trade 3+ --
input double LotSize3 = 0.01;               // Lot size for Trade 3+.
input double TP3 = 1000;                    // Take profit for Trade 3+ (default 1000 points).
input int SL3 = 0;
input int Trade3Rule = 0;
input string Comment3 = "Trade 3+";
input color Trade3Color = clrRed;
input bool EnableStaggeredOrders = false;   // Enable staggered order entries (true/false).
input double StaggeredOrderPercent = 0.2;   // Percent for staggered order offset.
input bool TightSL = false;                 // Enable tight stop loss (true/false).
input int TightSLPoints = 100;              // Points for tight stop loss.
input bool BreakevenRunner = false;         // Enable breakeven runner logic (true/false).
input string SymbolsList = "EURUSD,GBPUSD,USDJPY"; // List of symbols for multi-symbol trading.

// ================================
// Range Size & Spread Filters
// ================================
input string SECTION_RANGE = "--- Range Size & Spread Filters ---\nConfigure min/max range size, max spread, and pending order expiry for breakout trades.";
input double MinRangePoints = 5000;         // Minimum range size in points (no trade if range < this, 0 = disabled). Default 5000 to avoid too-small ranges.
input double MaxRangePoints = 0;            // Maximum range size in points (no trade if range > this, 0 = disabled).
input double MaxSpreadPoints = 50;          // No trade if spread > this.
// ================================
// Gap Day Filter (Server time)
// ================================
input bool EnableGapDayFilter = true;       // Skip trading if today's server-day range exceeds threshold (for NAS)
input int  MaxDailyRangePointsNAS = 40000;  // NASDAQ (NAS100/US100/USTEC) daily range threshold in points

input int PendingOrderExpiryHours = 0;      // Cancel pending if not triggered in X hours (0 = disabled).

// ================================
// Risk Management & Loss Limits
// ================================
input string SECTION_RISK = "--- Equity-Based Risk Cut (Daily & Weekly) ---\nMonitors AccountEquity live and, when limits are hit, closes all trades and deletes pending orders.";
input bool EnableRiskManagement = true;     // Enable equity-based risk cut system
input double StartingBalanceOverride = 0.0; // Manual starting balance (0 = auto-capture current balance).
// Daily limits (equity-based)
input double MaxDailyLossPercent = 4.0;     // Daily loss % from DayStartEquity (0=off)
input double MaxDailyLossAmount = 0.0;      // Daily loss $ from DayStartEquity (0=off)
// Weekly limits (equity-based)
input double MaxWeeklyLossPercent = 8.0;    // Weekly loss % from WeekStartEquity (0=off)
input double MaxWeeklyLossAmount = 0.0;     // Weekly loss $ from WeekStartEquity (0=off)
// Total equity floor
input double MaxTotalLossPercent = 8.0;     // Total loss % from StartEquity (0=off)
input double MaxTotalLossAmount = 0.0;      // Total loss $ from StartEquity (0=off)
input double EmergencyStopEquityPercent = 80.0; // If equity drops below this % of StartEquity, close all.
// Actions when a limit is hit
input bool CloseAllOnDailyLimit = true;     // Close all trades when daily limit is hit
input bool CloseAllOnWeeklyLimit = true;    // Close all trades when weekly limit is hit
input bool DeletePendingOnLimit = true;     // Delete pending orders when any limit is hit
input bool StopTradingOnWeeklyLimit = true; // Stop all trading when weekly limit hit (manual reset)
input bool StopTradingOnTotalLimit = true;  // Stop all trading when total limit hit (manual reset)
input bool ResetStartingBalanceDaily = false; // Reset starting balance daily (true) or keep original (false).

// ================================
// Volatility & ADR/ATR Filters
// ================================
input bool EnableATRFilter = false;         // Use ATR filter for range validation (true/false).
input int ATRPeriod = 14;                   // ATR calculation period.
input double MinRangeATR = 0.5;             // Minimum range as ATR multiple.
input double MaxRangeATR = 2.5;             // Maximum range as ATR multiple.
input bool UseADRLimit = false;             // Use ADR-based range filter (true/false).
input int ADRDays = 14;                     // ADR period (days).
input double MaxRangeADRMultiple = 1.5;     // Max allowed range as multiple of ADR.
input int ADRType = 0;                      // 0=Daily ADR, 1=Weekly ADR.

// ================================
// Visuals, Alerts & Panels
// ================================
input string SECTION_9 = "--- Visuals, Alerts & Panels ---\nCustomize chart visuals, enable/disable alerts, and show/hide the stats panel.";
input color RangeBoxColor = clrYellow;      // Color for the range box.
input color RangeTopLineColor = clrRed;     // Color for the top line.
input color RangeBottomLineColor = clrLime; // Color for the bottom line.
input int RangeBoxOpacity = 20;             // Opacity for the box fill (0-255).
input bool EnableStatsPanel = true;         // Show the stats panel (true/false).
input bool EnableAlerts = true;             // Enable alerts (true/false).

// ================================
// Dashboard Display Settings
// ================================
input string SECTION_DASHBOARD = "--- Dashboard Display Settings ---\nConfigure the trading dashboard for performance analysis and monitoring.";
input bool EnableDashboard = true;          // Show trading dashboard (true/false).
input int DashboardCorner = 0;              // Dashboard corner (0=top-left, 1=top-right, 2=bottom-left, 3=bottom-right).
input int DashboardXOffset = 10;            // Dashboard X offset from corner.
input int DashboardYOffset = 20;            // Dashboard Y offset from corner.
input color DashboardBackColor = clrDarkSlateGray;  // Dashboard background color.
input color DashboardTextColor = clrWhite;  // Dashboard text color.
input color DashboardProfitColor = clrLime; // Color for profit values.


input color DashboardLossColor = clrRed;    // Color for loss values.
input int DashboardFontSize = 8;            // Dashboard font size.
input string DashboardFont = "Arial";       // Dashboard font name.
input bool ShowAccountInfo = true;          // Show account balance and equity info.
input bool ShowDailyStats = true;           // Show daily profit/loss statistics.
input bool ShowWeeklyStats = true;          // Show weekly profit/loss statistics.
input bool ShowMonthlyStats = true;         // Show monthly profit/loss statistics.
input bool ShowRiskLimits = true;           // Show risk management limits and status.
input bool ShowTradeStats = true;           // Show trade statistics (win rate, etc.).
input bool ShowRangeInfo = true;            // Show current range information.

// ================================
// Fakeout & Reversal Logic
// ================================
input string SECTION_12 = "--- Fakeout & Reversal Logic ---\nSimplified fakeout logic using same session timing as normal trades.";
input bool EnableFakeoutFade = false;       // Use fakeout fade logic (true/false).
input bool EnableEnhancedFakeout = false;   // Enable enhanced fakeout logic (true/false).
input bool EnableReversalEntryLogic = false; // Enable/Disable Reversal Entry Logic.

// ================================
// Breakeven & Trailing by Profit Amount
// ================================
input bool UseBreakEvenProfitAmount = false;      // Enable BE by profit amount
input double BreakEvenProfitAmount = 0;          // Profit (in account currency) to trigger BE

input bool UseTrailingProfitAmount = false;       // Enable trailing by profit amount
input double TrailingProfitTrigger = 0;           // Profit (in account currency) to start trailing
input double TrailingProfitStep = 0;              // Trail distance (in account currency) behind profit

// ================================
// Trade Logic Timing
// ================================
input string SECTION_10 = "--- Trade Logic Timing ---\nConfigure the specific time for placing trades at the range boundaries.";
input bool EnableTradeLogicTime = false;    // Enable placing trades at a specific time (true/false).
input int TradeLogicHour = 14;              // Hour for trade logic (e.g., 14 for 2:00 PM Berlin time).
input int TradeLogicMinute = 30;            // Minute for trade logic (e.g., 30 for 2:30 PM).
input int FallbackCheckMinutes = 5;          // After session time, re-check every N minutes until placed

//--- ENUMS & TRADE RULE CONSTANTS ---
// Trade rule types for per-trade logic
enum TradeRuleType { RuleNormal = 0, RuleTightSL = 1, RuleBreakevenRunner = 2 };
#define RULE_NORMAL 0
#define RULE_TIGHTSL 1
#define RULE_BREAKEVENRUNNER 2

double CurrentADR = 0;                         // Calculated ADR for info panel
bool ADRTooBig = false;                        // Flag for range too big vs ADR

// Custom Trading Hours Variables
datetime TradingStart = 0;                     // Custom trading session start time
datetime TradingEnd = 0;                       // Custom trading session end time

// Enhanced Fakeout Logic Variables
bool FakeoutBuyOrderPlaced = false;            // Flag to track if fakeout buy order was placed
bool FakeoutSellOrderPlaced = false;           // Flag to track if fakeout sell order was placed
bool FakeoutLogicActive = false;               // Flag to track if fakeout logic is currently active

// Broker lot info (auto-detected)
double BrokerMinLot = 0.01;
double BrokerMaxLot = 100.0;

// Helper: Calculate lot size for risk
// Returns min lot if InitialTradeRiskPercent==0
// Otherwise, calculates lot size for given risk percent
// stopDist in points
// Returns lot size (rounded to broker min lot step)
double CalcInitialLot(double stopDist) {
   double lot = BrokerMinLot;

   // Update broker lot info
   BrokerMinLot = MarketInfo(Symbol(), MODE_MINLOT);
   BrokerMaxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double step = MarketInfo(Symbol(), MODE_LOTSTEP);

   if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0) {
      double riskMoney = AccountBalance() * InitialTradeRiskPercent / 100.0;
      lot = riskMoney / (stopDist * PointValue);

      // Round to broker lot step
      if(step > 0) {
         lot = MathFloor(lot / step) * step;
      }

      // Ensure within broker limits
      lot = MathMax(BrokerMinLot, lot);
      lot = MathMin(lot, BrokerMaxLot);

      Print("[DEBUG] CalcInitialLot: Risk=", InitialTradeRiskPercent, "%, StopDist=", stopDist,
            " pts, RiskMoney=$", riskMoney, ", CalculatedLot=", lot);
   }
   return lot;
}

//+------------------------------------------------------------------+
//| TRADING SESSIONS (Server time)                                    |
//| - 24hr: Range 00:00-06:00, Trades at 06:00 server                 |
//| - London: Range 00:00-06:00, Trades at 08:00 server               |
//| - US: Range 00:00-06:00, Trades at 14:00 server                   |
//| - All sessions close at 22:50 server (cleanup window 22:00–23:00) |
//+------------------------------------------------------------------+

// Old session settings removed - now using TradingSessionPreset dropdown

// Range time (editable)

// Global Variables
int BuyTicket1 = 0, BuyTicket2 = 0, SellTicket1 = 0, SellTicket2 = 0;
double RangeHigh = 0, RangeLow = 0;
datetime RangeStart, RangeEnd;
bool RangeIdentified = false;
string RangeBoxName = "";
string RangeTopLineName = "";
string RangeBottomLineName = "";
int EAInstanceID = 0;
int MagicBase = 10000;
double StartingBalance = 0;
double PerfWinRate = 0;
double PerfExpectancy = 0;
double PerfAvgWin = 0;
double PerfAvgLoss = 0;
int PerfMaxWinStreak = 0;
int PerfMaxLossStreak = 0;
double MaxDrawdown = 0;

// Last server day marker for daily reset
datetime LastTradeDayReset = 0;

// Performance tracking
// ... (same as before, omitted here for brevity)

// Global Variables for Daily Trade Limits
int DailyTradesCount = 0;
int DailyReversalTradesCount = 0;
bool BreakoutTradesPlacedToday = false; // NEW: Track if breakout trades have been placed today

//+------------------------------------------------------------------+
//| Dashboard & Performance Tracking Variables                       |
//+------------------------------------------------------------------+
// Account tracking
double SessionStartBalance = 0;
double SessionStartEquity = 0;
double MonthStartBalance = 0;
double MonthStartEquity = 0;

// Daily performance
double DailyProfitAmount = 0;
double DailyProfitPercent = 0;
double DailyLossAmount = 0;
double DailyLossPercent = 0;
int DailyWins = 0;
int DailyLosses = 0;

// Weekly performance
double WeeklyProfitAmount = 0;
double WeeklyProfitPercent = 0;
double WeeklyLossAmount = 0;
double WeeklyLossPercent = 0;
int WeeklyWins = 0;
int WeeklyLosses = 0;

// Monthly performance
double MonthlyProfitAmount = 0;
double MonthlyProfitPercent = 0;
double MonthlyLossAmount = 0;
double MonthlyLossPercent = 0;
int MonthlyWins = 0;
int MonthlyLosses = 0;

// Risk management status
bool TotalLossLimitHit = false;
bool TradingPermanentlyStopped = false;  // Flag for permanent trading stop
string TradingStopReason = "";           // Reason for trading stop
double CurrentDrawdown = 0;
double MaxDrawdownPercent = 0;
double MaxDrawdownAmount = 0;

// Dashboard objects
string DashboardPrefix = "Dashboard_";
int DashboardLineHeight = 15;
int DashboardCurrentY = 0;

// Time tracking for resets
int LastMonth = -1;
datetime LastSessionReset = 0;

//+------------------------------------------------------------------+
//| Risk tracking variables                                          |
//+------------------------------------------------------------------+

// ---- PROPER IMPLEMENTATIONS FOR MISSING FUNCTIONS ----
string StringTrim(string s) {
   while(StringLen(s) > 0 && (StringGetCharacter(s, 0) == ' ' || StringGetCharacter(s, 0) == '\t' || StringGetCharacter(s, 0) == '\n' || StringGetCharacter(s, 0) == '\r')) {
      s = StringSubstr(s, 1);
   }
   while(StringLen(s) > 0 && (StringGetCharacter(s, StringLen(s)-1) == ' ' || StringGetCharacter(s, StringLen(s)-1) == '\t' || StringGetCharacter(s, StringLen(s)-1) == '\n' || StringGetCharacter(s, StringLen(s)-1) == '\r')) {
      s = StringSubstr(s, 0, StringLen(s)-1);
   }
   return s;
}

string ErrorDescription(int code) {
   switch(code) {
      case 0: return "No error";
      case 1: return "No error returned";
      case 2: return "Common error";
      case 3: return "Invalid trade parameters";
      case 4: return "Trade server is busy";
      case 5: return "Old version of the client terminal";
      case 6: return "No connection with trade server";
      case 7: return "Not enough rights";
      case 8: return "Too frequent requests";
      case 9: return "Malfunctional trade operation";
      case 64: return "Account disabled";
      case 65: return "Invalid account";
      case 128: return "Trade timeout";
      case 129: return "Invalid price";
      case 130: return "Invalid stops";
      case 131: return "Invalid trade volume";
      case 132: return "Market is closed";
      case 133: return "Trade is disabled";
      case 134: return "Not enough money";
      case 135: return "Price changed";
      case 136: return "Off quotes";
      case 137: return "Broker is busy";
      case 138: return "Requote";
      case 139: return "Order is locked";
      case 140: return "Long positions only allowed";
      case 141: return "Too many requests";
      case 145: return "Modification denied because order too close to market";
      case 146: return "Trade context is busy";
      case 147: return "Expirations are denied by broker";
      case 148: return "Amount of open and pending orders has reached the limit";
      default: return "Unknown error " + IntegerToString(code);
   }
}
// -------------------------------------

double StartEquity = 0;
double DayStartEquity = 0;
double WeekStartEquity = 0;
bool DayLossLimitHit = false;
bool WeekLossLimitHit = false;
bool EmergencyStopHit = false;
int LastDay = -1;
int LastWeek = -1;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int AssetClass() {
   string sym = Symbol();

   // Manual uppercase conversion (StringToUpper doesn't exist in MQL4)
   string symUpper = "";
   for(int i = 0; i < StringLen(sym); i++) {
      ushort ch = (ushort)StringGetCharacter(sym, i);
      if(ch >= 97 && ch <= 122) ch = ch - 32; // Convert lowercase to uppercase
      string charStr = CharToString((char)ch);
      StringConcatenate(symUpper, symUpper, charStr);
   }

   // Check for indices, metals, and crypto symbols
   if(StringFind(symUpper, "NAS") >= 0 || StringFind(symUpper, "SPX") >= 0 || StringFind(symUpper, "DAX") >= 0 ||
      StringFind(symUpper, "DJ") >= 0 || StringFind(symUpper, "UK") >= 0 || StringFind(symUpper, "HK") >= 0 ||
      StringFind(symUpper, "BTC") >= 0 || StringFind(symUpper, "ETH") >= 0 || StringFind(symUpper, "LTC") >= 0 ||
      StringFind(symUpper, "XAU") >= 0 || StringFind(symUpper, "XAG") >= 0 || StringFind(symUpper, "GOLD") >= 0 || StringFind(symUpper, "SILVER") >= 0)
      return 1; // Indices/Metals/Crypto
   return 0; // Forex
}

// Unique EA instance ID and magic number base

double ActualLotSize1 = 0.01; // Effective lot size for Trade 1

// CRITICAL FIX: Helper function to set BreakoutTradesPlacedToday and save to GlobalVariable
void SetBreakoutTradesPlacedToday(bool value) {
    BreakoutTradesPlacedToday = value;
    string globalVarName = "BreakoutTradesPlaced_" + Symbol() + "_" + IntegerToString(MagicBase) + "_" + IntegerToString(AccountNumber());
    datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE)); // server midnight

    if(value) {
        GlobalVariableSet(globalVarName, 1.0);
        GlobalVariableSet(globalVarName + "_Date", today);
        Print("[PERSISTENCE] [", globalVarName, "] BreakoutTradesPlacedToday=true saved (server midnight)");
    } else {
        GlobalVariableDel(globalVarName);
        GlobalVariableDel(globalVarName + "_Date");
        Print("[PERSISTENCE] [", globalVarName, "] BreakoutTradesPlacedToday=false cleared");
    }
}

int OnInit() {
    // --- Auto-sync ActualLotSize1 with LotSize if user changed LotSize but left LotSize1 at default ---
    if (MathAbs(LotSize1 - 0.01) < 1e-8 && MathAbs(LotSize - 0.01) > 1e-8) {
        ActualLotSize1 = LotSize;
        Print("[INFO] ActualLotSize1 auto-set to LotSize (" + DoubleToString(LotSize, 2) + ") because LotSize1 was left at default."); // No implicit conversion warning now
    } else {
        ActualLotSize1 = LotSize1;
    }

   // Assign unique EA instance ID and magic number base
   EAInstanceID = (int)ChartID();
   MagicBase = 10000 + (EAInstanceID % 90000);

   // CRITICAL: Check for permanent trading stop status
   string stopGlobalVarName = "TradingStop_" + Symbol() + "_" + IntegerToString(MagicBase);
   double stopStatus = GlobalVariableGet(stopGlobalVarName);
   if(stopStatus == 1.0) {
       TradingPermanentlyStopped = true;
       TradingStopReason = "Previous Loss Limit Hit";
       Alert("TRADING STOPPED: EA was previously stopped due to loss limits. Manual reset required.");
       Print("[CRITICAL] Trading is permanently stopped. Previous loss limit was hit. Remove EA and re-attach to reset.");
       Comment("⚠️ TRADING STOPPED ⚠️\nPrevious Loss Limit Hit!\nMANUAL RESET REQUIRED\nRemove EA and re-attach to resume");
       return(INIT_FAILED); // Prevent EA from running
   }

   // CRITICAL FIX: Restore BreakoutTradesPlacedToday from GlobalVariable to prevent multiple trades on EA restart
   string globalVarName = "BreakoutTradesPlaced_" + Symbol() + "_" + IntegerToString(MagicBase) + "_" + IntegerToString(AccountNumber());
   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE)); // server midnight
   double storedValue = GlobalVariableGet(globalVarName);
   double storedDate = GlobalVariableGet(globalVarName + "_Date");

   if(storedDate == today && storedValue == 1.0) {
       BreakoutTradesPlacedToday = true;
       Print("[PERSISTENCE] [", globalVarName, "] Restored BreakoutTradesPlacedToday=true (trades already placed today)");
   } else {
       BreakoutTradesPlacedToday = false;
       GlobalVariableDel(globalVarName);         // clear any stale key from other account/day
       GlobalVariableDel(globalVarName + "_Date");
       Print("[PERSISTENCE] [", globalVarName, "] BreakoutTradesPlacedToday=false (new day/account or no previous trades)");
   }

   // Input validation
   string errMsg = "";
   if(LotSize <= 0) errMsg += "\nLot size must be positive.";
   if(InitialTradeRiskPercent < 0 || InitialTradeRiskPercent > 100) errMsg += "\nInitialTradeRiskPercent must be 0-100.";
   if(TradeTargetR < 0) errMsg += "\nTradeTargetR must be >= 0.";
   if(TradeTargetPoints < 0) errMsg += "\nTradeTargetPoints must be >= 0.";
   if(RiskAmount < 0) errMsg += "\nRiskAmount must be >= 0.";
   if(MaxDailyLossPercent < 0 || MaxDailyLossPercent > 100) errMsg += "\nMaxDailyLossPercent must be 0-100.";
   if(MaxWeeklyLossPercent < 0 || MaxWeeklyLossPercent > 100) errMsg += "\nMaxWeeklyLossPercent must be 0-100.";
   if(EmergencyStopEquityPercent < 1 || EmergencyStopEquityPercent > 100) errMsg += "\nEmergencyStopEquityPercent must be 1-100.";
   if(MinRangePoints < 0) errMsg += "\nMinRangePoints must be >= 0.";
   if(MaxRangePoints < 0) errMsg += "\nMaxRangePoints must be >= 0.";
   if(MaxSpreadPoints < 0) errMsg += "\nMaxSpreadPoints must be >= 0.";
   if(ADRDays < 1) errMsg += "\nADRDays must be >= 1.";
   if(MaxRangeADRMultiple < 0) errMsg += "\nMaxRangeADRMultiple must be >= 0.";
   if(AddedStopLoss < 0) errMsg += "\nAddedStopLoss must be >= 0.";
   if(errMsg != "") {
      Alert(StringFormat("EA initialization error: %s", errMsg));
      Print(StringFormat("EA initialization error: %s", errMsg));
      ExpertRemove();
      return(INIT_FAILED);
   }
   Print("[DEBUG] OnInit: Input RangeStartHour=", RangeStartHour, ", RangeStartMinute=", RangeStartMinute, ", RangeEndHour=", RangeEndHour, ", RangeEndMinute=", RangeEndMinute);

   // Broker server time only; timezone guidance removed
   Print("[TIME] Broker server time is used everywhere. Ensure chart time matches your broker's server.");
   // Risk tracking initialization
   // Handle starting balance - use override if provided, otherwise auto-capture
   if(StartingBalanceOverride > 0) {
      StartingBalance = StartingBalanceOverride;
      StartEquity = StartingBalanceOverride;
      Print("[INIT] Using manual starting balance: ", DoubleToString(StartingBalance, 2));
   } else {
      StartingBalance = AccountBalance();
      StartEquity = AccountEquity();
      Print("[INIT] Auto-captured starting balance: ", DoubleToString(StartingBalance, 2));
   }

   DayStartEquity = AccountEquity();
   WeekStartEquity = AccountEquity();
   SessionStartBalance = AccountBalance();
   SessionStartEquity = AccountEquity();
   MonthStartBalance = AccountBalance();
   MonthStartEquity = AccountEquity();
   DayLossLimitHit = false;
   WeekLossLimitHit = false;
   TotalLossLimitHit = false;
   EmergencyStopHit = false;
   LastDay = TimeDayOfYear(TimeCurrent());
   LastWeek = TimeDayOfYear(TimeCurrent())/7;
   LastMonth = TimeMonth(TimeCurrent());
   LastTradeDayReset = TimeCurrent(); // Initialize LastTradeDayReset
   DailyTradesCount = 0; // Initialize DailyTradesCount
   DailyReversalTradesCount = 0; // Initialize DailyReversalTradesCount

   // Initialize performance tracking
   DailyWins = 0;
   DailyLosses = 0;
   WeeklyWins = 0;
   WeeklyLosses = 0;
   MonthlyWins = 0;
   MonthlyLosses = 0;
   MaxDrawdownPercent = 0;
   MaxDrawdownAmount = 0;
   // Auto-set BETriggerPoints/Percent based on asset class, unless user changed them
   static bool beSet = false;
   static int beTriggerPoints = 0;
   static double beTriggerPercent = 0;
   if(!beSet) {
      beTriggerPoints = BETriggerPoints;
      beTriggerPercent = BETriggerPercent;
      if(AssetClass() == 1) {
         if(beTriggerPoints == 0) beTriggerPoints = 3000;
         if(beTriggerPercent == 0) beTriggerPercent = 1.5;
      } else {
         if(beTriggerPoints == 0) beTriggerPoints = 300;
         if(beTriggerPercent == 0) beTriggerPercent = 1.5;
      }
      beSet = true;
   }
   EventSetTimer(60);
   ResetRangeTimes();
   ResetTradingHours();
   // Fakeout logic now uses same session timing as normal trades

   // NEW: Check for immediate trade placement when EA is loaded mid-day
   CheckImmediateTradePlacement();

   // Print comprehensive status for troubleshooting
   PrintEAStatus();

   // ... Initialize performance tracking and counters
   UpdateInfoPanel();
   DrawRangeVisuals();
   return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   DeleteRangeVisuals();
   DeleteDashboard();
   EventKillTimer();
   Comment("");
}

//+------------------------------------------------------------------+
//| Risk limit logic                                                 |
//+------------------------------------------------------------------+
void UpdateRiskLimits() {
   if(!EnableRiskManagement) return;

   datetime now = TimeCurrent();
   int day = TimeDayOfYear(now);
   int week = day/7;
   int month = TimeMonth(now);

   // New day/week/month resets
   if(day != LastDay) {
      DayStartEquity = AccountEquity();
      SessionStartBalance = AccountBalance();
      SessionStartEquity = AccountEquity();
      DayLossLimitHit = false;
      LastDay = day;
      DailyWins = 0;
      DailyLosses = 0;

      // Reset starting balance daily if enabled
      if(ResetStartingBalanceDaily) {
         if(StartingBalanceOverride > 0) {
            StartingBalance = StartingBalanceOverride;
            StartEquity = StartingBalanceOverride;
            Print("[DAILY RESET] Using manual starting balance: ", DoubleToString(StartingBalance, 2));
         } else {
            StartingBalance = AccountBalance();
            StartEquity = AccountEquity();
            Print("[DAILY RESET] Reset starting balance to current: ", DoubleToString(StartingBalance, 2));
         }
      }
   }
   if(week != LastWeek) {
      WeekStartEquity = AccountEquity();
      WeekLossLimitHit = false;
      LastWeek = week;
      WeeklyWins = 0;
      WeeklyLosses = 0;
   }
   if(month != LastMonth) {
      MonthStartBalance = AccountBalance();
      MonthStartEquity = AccountEquity();
      LastMonth = month;
      MonthlyWins = 0;
      MonthlyLosses = 0;
   }

   // Calculate losses
   double currentEquity = AccountEquity();
   double dayLossPercent = (DayStartEquity - currentEquity) / DayStartEquity * 100.0;
   double dayLossAmount = DayStartEquity - currentEquity;
   double weekLossPercent = (WeekStartEquity - currentEquity) / WeekStartEquity * 100.0;
   double weekLossAmount = WeekStartEquity - currentEquity;
   double totalLossPercent = (StartEquity - currentEquity) / StartEquity * 100.0;
   double totalLossAmount = StartEquity - currentEquity;
   double emergLoss = (currentEquity / StartEquity) * 100.0;

   // Update performance tracking
   DailyProfitAmount = currentEquity - DayStartEquity;
   DailyProfitPercent = DailyProfitAmount / DayStartEquity * 100.0;
   WeeklyProfitAmount = currentEquity - WeekStartEquity;
   WeeklyProfitPercent = WeeklyProfitAmount / WeekStartEquity * 100.0;
   MonthlyProfitAmount = currentEquity - MonthStartEquity;
   MonthlyProfitPercent = MonthlyProfitAmount / MonthStartEquity * 100.0;

   // Calculate drawdown
   CurrentDrawdown = (StartEquity - currentEquity) / StartEquity * 100.0;
   if(CurrentDrawdown > MaxDrawdownPercent) {
      MaxDrawdownPercent = CurrentDrawdown;
      MaxDrawdownAmount = StartEquity - currentEquity;
   }

   // Check loss limits
   bool dailyPercentHit = (MaxDailyLossPercent > 0 && dayLossPercent >= MaxDailyLossPercent);
   bool dailyAmountHit = (MaxDailyLossAmount > 0 && dayLossAmount >= MaxDailyLossAmount);
   bool weeklyPercentHit = (MaxWeeklyLossPercent > 0 && weekLossPercent >= MaxWeeklyLossPercent);
   bool weeklyAmountHit = (MaxWeeklyLossAmount > 0 && weekLossAmount >= MaxWeeklyLossAmount);
   bool totalPercentHit = (MaxTotalLossPercent > 0 && totalLossPercent >= MaxTotalLossPercent);
   bool totalAmountHit = (MaxTotalLossAmount > 0 && totalLossAmount >= MaxTotalLossAmount);

   DayLossLimitHit = dailyPercentHit || dailyAmountHit;
   WeekLossLimitHit = weeklyPercentHit || weeklyAmountHit;
   TotalLossLimitHit = totalPercentHit || totalAmountHit;
   EmergencyStopHit = (emergLoss <= EmergencyStopEquityPercent);

   // Handle loss limit actions
   if(DayLossLimitHit || WeekLossLimitHit || TotalLossLimitHit || EmergencyStopHit) {
      string limitType = "";
      if(DayLossLimitHit) limitType = "Daily";
      else if(WeekLossLimitHit) limitType = "Weekly";
      else if(TotalLossLimitHit) limitType = "Total";
      else if(EmergencyStopHit) limitType = "Emergency";

      Print("[RISK MANAGEMENT] ", limitType, " loss limit hit! Current loss: ", DoubleToString(dayLossPercent, 2), "%");

      if(DeletePendingOnLimit) {
         DeletePendingOrders();
         Print("[RISK MANAGEMENT] All pending orders deleted due to loss limit.");
      }

      if((CloseAllOnDailyLimit && DayLossLimitHit) ||
         (CloseAllOnWeeklyLimit && WeekLossLimitHit) ||
         TotalLossLimitHit || EmergencyStopHit) {
         CloseAllPositions();
         Print("[RISK MANAGEMENT] All positions closed due to loss limit.");
      }

      // Permanent trading stop for weekly/total limits
      if((WeekLossLimitHit && StopTradingOnWeeklyLimit) ||
         (TotalLossLimitHit && StopTradingOnTotalLimit) ||
         EmergencyStopHit) {
         if(!TradingPermanentlyStopped) {
            TradingPermanentlyStopped = true;
            TradingStopReason = limitType + " Loss Limit Hit";

            // Save permanent stop status to global variable for persistence
            string globalVarName = "TradingStop_" + Symbol() + "_" + IntegerToString(MagicBase);
            GlobalVariableSet(globalVarName, 1.0);
            GlobalVariableSet(globalVarName + "_Reason", StringToDouble(TradingStopReason)); // Store as encoded value

            Alert("CRITICAL: ", limitType, " loss limit hit! Trading permanently stopped. EA must be manually reset to resume trading.");
            Print("[CRITICAL] Trading permanently stopped due to ", limitType, " loss limit. Manual reset required.");
            Print("[CRITICAL] To resume trading: Remove EA from chart and re-attach with fresh settings.");

            // Show critical warning on chart
            Comment("⚠️ TRADING STOPPED ⚠️\n" +
                   limitType + " Loss Limit Hit!\n" +
                   "Current Loss: " + DoubleToString((limitType == "Daily" ? dayLossPercent : (limitType == "Weekly" ? weekLossPercent : totalLossPercent)), 2) + "%\n" +
                   "MANUAL RESET REQUIRED\n" +
                   "Remove EA and re-attach to resume");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
   DrawRangeVisuals();
   datetime currentTime = TimeCurrent();
   int currentHour = TimeHour(currentTime);
   int currentMinute = TimeMinute(currentTime);
   // Cancel all orders at 10 PM (22:00)
   if(currentHour == 22 && currentMinute == 0) {
      Print("[DEBUG] 22:00 daily check: Managing end-of-day positions and orders.");
      ManageEndOfDayPositions();
      DeletePendingOrders();
   }
   // Also run session end logic
   if(IsTradingEnd(currentHour, currentMinute)) {
      Print("[DEBUG] End of trading session: Managing end-of-day positions and orders.");
      ManageEndOfDayPositions();
      DeletePendingOrders();
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // RISK MANAGEMENT DISABLED - TRADING ALLOWED
    /*
    // DISABLED: Check if trading is permanently stopped
    if(TradingPermanentlyStopped) {
        // Show warning and prevent all trading activity
        static datetime lastWarning = 0;
        if(TimeCurrent() - lastWarning >= 300) { // Show warning every 5 minutes
            Comment("⚠️ TRADING STOPPED ⚠️\n" + TradingStopReason + "\nMANUAL RESET REQUIRED\nRemove EA and re-attach to resume");
            Print("[WARNING] Trading is permanently stopped. Reason: ", TradingStopReason);
            lastWarning = TimeCurrent();
        }
        return; // Exit OnTick completely - no trading activity allowed
    }
    */

    datetime currentTime = TimeCurrent();

    // Use a separate variable to manage EnableTradeLogicTime state
    bool tradeLogicTimeEnabled = EnableTradeLogicTime;
    if (TradingSessionPreset != SESSION_24HR) {
        tradeLogicTimeEnabled = false;
    }

    // --- 22:50 Server time: End-of-day logic (10 minutes before broker midnight) ---
    static datetime lastEODServer = 0;
    int srvHour = TimeHour(currentTime);
    int srvMin  = TimeMinute(currentTime);
    if(srvHour == 22 && srvMin == 50) {
        datetime serverMidnight = StringToTime(TimeToString(currentTime, TIME_DATE));
        datetime thisEOD = serverMidnight + 22*3600 + 50*60;
        if(lastEODServer != thisEOD) {
            Print("[EOD] 22:50 Server: Running end-of-day trade management.");
            ManageEndOfDayServer();
            lastEODServer = thisEOD;
        }
        // Prevent further trading logic this tick
        return;
    }

    // 22:00–23:00 server: double-sweep pendings in case of freeze
    static datetime lastFreezeSweep = 0;
    datetime srvToday0 = StringToTime(TimeToString(currentTime, TIME_DATE));
    datetime thisMinute = srvToday0 + TimeHour(currentTime)*3600 + TimeMinute(currentTime)*60;
    if(TimeHour(currentTime) >= 22 && TimeHour(currentTime) < 23) {
        if(lastFreezeSweep != thisMinute) {
            Print("[EOD] Server freeze window sweep (22:00–23:00): Deleting any remaining pending orders.");
            DeletePendingOrders();
            lastFreezeSweep = thisMinute;
        }
    }

    // 00:00–06:00 server: ensure no pendings roll into new day
    static datetime lastPurgeDay = 0;
    if(TimeHour(currentTime) >= 0 && TimeHour(currentTime) < 6) {
        datetime today0 = srvToday0;
        if(lastPurgeDay != today0) {
            Print("[CLEANUP] Server midnight purge: removing stale pending orders from previous day.");
            PurgeStalePendingsForToday();
            lastPurgeDay = today0;
        }
    }

    // --- STRATEGY TESTER SUPPORT: Force daily range creation ---
    static datetime lastTesterDay = 0;
    bool isTester = IsTesting();
    datetime today = StringToTime(TimeToString(currentTime, TIME_DATE));
    if (isTester && today != lastTesterDay) {
      // Simulate new day in tester: reset range and trading flags
      DayStartEquity = AccountEquity();
      DayLossLimitHit = false;
      ResetRangeTimes();
      ResetTradingHours();
      RangeIdentified = false;
      DeleteRangeVisuals();
      DeletePendingOrders();
      FakeoutBuyOrderPlaced = false;
      FakeoutSellOrderPlaced = false;
      FakeoutLogicActive = false;
      DailyTradesCount = 0;
      DailyReversalTradesCount = 0;
      LastTradeDayReset = currentTime;
      lastTesterDay = today;
      SetBreakoutTradesPlacedToday(false); // NEW: Reset on new tester day

      // Reset fake out variables
      fakeoutUp = false;
      fakeoutDown = false;
      fakeoutChecked = false;
      Print("[DEBUG] [TESTER] New day detected. Range times reset and GlobalVariable cleared. Tester date: ", TimeToString(today, TIME_DATE));
   }

   // --- DAILY RESET: If new server day, reset range and flags ---
   datetime serverDay = StringToTime(TimeToString(currentTime, TIME_DATE));
   if(LastTradeDayReset < serverDay) {
      DayStartEquity = AccountEquity();
      DayLossLimitHit = false;
      // Reset range and trading flags
      ResetRangeTimes();
      ResetTradingHours();
      RangeIdentified = false;
      DeleteRangeVisuals();
      DeletePendingOrders();
      FakeoutBuyOrderPlaced = false;
      FakeoutSellOrderPlaced = false;
      FakeoutLogicActive = false;
      DailyTradesCount = 0;
      DailyReversalTradesCount = 0;
      LastTradeDayReset = currentTime;
      SetBreakoutTradesPlacedToday(false); // NEW: Reset on new server day

      // Reset fake out variables
      fakeoutUp = false;
      fakeoutDown = false;
      fakeoutChecked = false;
      Print("[DEBUG] OnTick: New server day detected. Range times reset and GlobalVariable cleared. Server date: ", TimeToString(serverDay, TIME_DATE));
   }

   // --- CLEAR/HIDE RANGE BETWEEN 11:00-12:00 server time ---
   if(TimeHour(currentTime) >= 11 && TimeHour(currentTime) < 12) {
      if(RangeIdentified) {
         RangeIdentified = false;
         DeleteRangeVisuals();
         Print("[DEBUG] Server 11:00-12:00: Range cleared/hidden.");
      }
      // Prevent trading during this hour
      return;
   }

   // --- Create range first (always at end of range period) ---
   // use previously computed server time components
   // srvHour and srvMin were defined above

   if(!RangeIdentified && srvHour == RangeEndHour && srvMin == RangeEndMinute) {
      // Purge any stale EA pending orders from previous days (e.g., after weekend)
      PurgeStalePendingsForToday();
      Print("[DEBUG] Range period ended (server). Purged stale pendings. Identifying range...");
      IdentifyRange();
      RangeIdentified = true;
   }

   // --- Place trades at session time ---
   static datetime lastTradeTime = 0;
   int tradeHour = 6, tradeMinute = 0; // Default 24hr session
   string sessionName = "24hr";

   // Determine trade placement time based on session preset
   switch (TradingSessionPreset) {
      case SESSION_24HR: // 24hr - place at 06:00 (1 hour after range ends)
         tradeHour = 6; tradeMinute = 0; sessionName = "24hr";
         break;
      case SESSION_LONDON: // London session
         tradeHour = 8; tradeMinute = 0; sessionName = "London";
         break;
      case SESSION_US: // US session
         tradeHour = 14; tradeMinute = 0; sessionName = "US";
         break;
      default:
         tradeHour = 6; tradeMinute = 0; sessionName = "Default";
         break;
   }

   // --- Check for fakeout before session opens ---
   if(RangeIdentified) {
      CheckFakeoutBeforeSession();
   }

   // Debug logging for session timing (server time)
   if (srvHour == tradeHour && srvMin == tradeMinute) {
      Print("[SESSION] ", sessionName, " session time reached (", StringFormat("%02d:%02d", tradeHour, tradeMinute), " server)");
      Print("[SESSION] RangeIdentified = ", RangeIdentified, ", BreakoutTradesPlacedToday = ", BreakoutTradesPlacedToday);
      if(RangeIdentified) {
         double currentPrice = iClose(Symbol(), 0, 0);
         Print("[SESSION] Current price: ", DoubleToString(currentPrice, Digits), ", Range: ", DoubleToString(RangeLow, Digits), " - ", DoubleToString(RangeHigh, Digits));
      }
   }

   if (RangeIdentified && srvHour == tradeHour && srvMin == tradeMinute) {
      datetime serverMidnight = StringToTime(TimeToString(currentTime, TIME_DATE));
      datetime currentTradeTime = serverMidnight + tradeHour*3600 + tradeMinute*60;
      if (lastTradeTime != currentTradeTime && !BreakoutTradesPlacedToday) {
         Print("[DEBUG] ", sessionName, " session (", tradeHour, ":", tradeMinute, " server): Placing breakout trades.");
         Print("[DEBUG] EnableFakeoutFade = ", EnableFakeoutFade, ", fakeoutUp = ", fakeoutUp, ", fakeoutDown = ", fakeoutDown);
         Print("[DEBUG] Current price = ", DoubleToString(iClose(Symbol(), 0, 0), Digits), ", RangeHigh = ", DoubleToString(RangeHigh, Digits), ", RangeLow = ", DoubleToString(RangeLow, Digits));
         if (!ADRTooBig) {
            PlacePendingOrders();
            SetBreakoutTradesPlacedToday(true);
            lastTradeTime = currentTradeTime;
         } else {
            Print("Range too large compared to ADR, skipping trades for today.");
         }
      } else {
         Print("[DEBUG] Trade placement skipped - lastTradeTime: ", TimeToString(lastTradeTime), ", currentTradeTime: ", TimeToString(currentTradeTime));
      }
   }

   // --- Fallback: hourly placement check after scheduled session time (server) ---
   // If we missed the exact session minute (e.g., no tick at 06:00 server),
   // check every top of the hour until trades are placed.
   if(RangeIdentified && !BreakoutTradesPlacedToday) {
      datetime serverMidnight = StringToTime(TimeToString(currentTime, TIME_DATE));
      datetime scheduledTime = serverMidnight + tradeHour*3600 + tradeMinute*60;
      if (TimeCurrent() > scheduledTime) {
         static datetime lastFallbackHour = 0;
         datetime thisHour = serverMidnight + TimeHour(currentTime)*3600; // Top of current server hour
         if(TimeMinute(currentTime) == 0 && lastFallbackHour != thisHour) {
            Print("[FALLBACK] Top-of-hour (server): attempting placement per price vs range state. ADRTooBig=", (ADRTooBig?"true":"false"));
            if(!ADRTooBig) {
               PlacePendingOrders();
               // PlacePendingOrders sets the daily flag internally when successful
            } else {
               Print("[FALLBACK] Skipped due to ADRTooBig.");
            }
            lastFallbackHour = thisHour;
         }
      }
   }

   // --- Fallback: N-minute placement checks after scheduled session time (server) ---
   // Re-check every FallbackCheckMinutes (default 5) until trades are placed
   if(RangeIdentified && !BreakoutTradesPlacedToday) {
      datetime serverMidnight = StringToTime(TimeToString(currentTime, TIME_DATE));
      datetime scheduledTime = serverMidnight + tradeHour*3600 + tradeMinute*60;
      if (TimeCurrent() > scheduledTime) {
         static datetime lastFallbackCheck = 0;
         datetime thisCheck = serverMidnight + TimeHour(currentTime)*3600 + TimeMinute(currentTime)*60;
         int interval = MathMax(1, FallbackCheckMinutes);
         bool onInterval = (TimeMinute(currentTime) % interval) == 0;
         if(onInterval && lastFallbackCheck != thisCheck) {
            Print("[FALLBACK] N-min (server): attempting placement per price vs range state. ADRTooBig=", (ADRTooBig?"true":"false"));
            if(!ADRTooBig) {
               PlacePendingOrders();
               // PlacePendingOrders sets the daily flag internally when successful
            } else {
               Print("[FALLBACK] Skipped due to ADRTooBig.");
            }
            lastFallbackCheck = thisCheck;
         }
      }
   }



   // Enhanced Fakeout Logic - Check for fakeout opportunities
   CheckEnhancedFakeout();

   // Disable reversal trades when session presets are selected
   bool reversalEntryLogicEnabled = EnableReversalEntryLogic;
   if (TradingSessionPreset != SESSION_24HR) {
        reversalEntryLogicEnabled = false;
    }

    // Monitor for closed trades and update statistics
    MonitorClosedTrades();

    // Update risk management and performance tracking
    UpdateRiskLimits();
    UpdateTradeStatistics();

    // Update dashboard display
    static datetime lastDashboardUpdate = 0;
    if(TimeCurrent() - lastDashboardUpdate >= 1) { // Update every second
        UpdateInfoPanel();
        lastDashboardUpdate = TimeCurrent();
    }

    // Manage positions
    ManagePositions();

    // --- Place trades at the specified trade logic time ---
    if (tradeLogicTimeEnabled && RangeIdentified && TimeHour(currentTime) == TradeLogicHour && TimeMinute(currentTime) == TradeLogicMinute) {
        static datetime lastTradeLogicTime = 0;
        datetime serverMidnight = StringToTime(TimeToString(currentTime, TIME_DATE));
        datetime currentTradeLogicTime = serverMidnight + TradeLogicHour * 3600 + TradeLogicMinute * 60;

        if (lastTradeLogicTime != currentTradeLogicTime && !BreakoutTradesPlacedToday) {
            Print("[DEBUG] Trade logic time reached (", TradeLogicHour, ":", TradeLogicMinute, " server): Placing trades at range boundaries.");
            PlacePendingOrders();
            SetBreakoutTradesPlacedToday(true);
            lastTradeLogicTime = currentTradeLogicTime;
        } else if (BreakoutTradesPlacedToday) {
            Print("[DEBUG] Trade logic time reached but breakout trades already placed today. Skipping.");
        }
    }
}

//+------------------------------------------------------------------+
//| Print comprehensive EA status for troubleshooting               |
//+------------------------------------------------------------------+
void PrintEAStatus() {
   datetime currentTime = TimeCurrent();
   int srvH = TimeHour(currentTime), srvM = TimeMinute(currentTime);

   // Determine session info (server time)
   string sessionName = "24hr";
   int tradeHour = 6, tradeMinute = 0;
   switch (TradingSessionPreset) {
      case SESSION_24HR: tradeHour = 6; tradeMinute = 0; sessionName = "24hr"; break;
      case SESSION_LONDON: tradeHour = 8; tradeMinute = 0; sessionName = "London"; break;
      case SESSION_US: tradeHour = 14; tradeMinute = 0; sessionName = "US"; break;
   }

   Print("=== EA STATUS REPORT ===");
   Print("[STATUS] Current Server Time: ", StringFormat("%02d:%02d", srvH, srvM));
   Print("[STATUS] Selected Session: ", sessionName, " (server trades at ", StringFormat("%02d:%02d", tradeHour, tradeMinute), ")");
   Print("[STATUS] Range Period: ", StringFormat("%02d:%02d", RangeStartHour, RangeStartMinute), " - ", StringFormat("%02d:%02d", RangeEndHour, RangeEndMinute));
   Print("[STATUS] Range Identified: ", RangeIdentified);
   if(RangeIdentified) {
      Print("[STATUS] Range: ", DoubleToString(RangeLow, Digits), " - ", DoubleToString(RangeHigh, Digits));
      Print("[STATUS] Range Size: ", DoubleToString(RangeHigh - RangeLow, Digits));
   }
   Print("[STATUS] Trades Placed Today: ", BreakoutTradesPlacedToday);
   Print("[STATUS] Fakeout Enabled: ", EnableFakeoutFade);
   Print("========================");
}

//+------------------------------------------------------------------+
//| Helper: Check for immediate trade placement when EA loads mid-day|
//+------------------------------------------------------------------+
void CheckImmediateTradePlacement() {
   datetime currentTime = TimeCurrent();

   // First, try to identify range if it's past range end time
   // CRITICAL FIX: Only identify range if it hasn't been identified today AND no trades placed
   int currentServerHour = TimeHour(TimeCurrent());
   int currentServerMin = TimeMinute(TimeCurrent());
   if(currentServerHour >= RangeEndHour && !RangeIdentified && !BreakoutTradesPlacedToday) {
      Print("[IMMEDIATE] EA loaded after range period (", StringFormat("%02d:%02d", currentServerHour, currentServerMin), " >= ", StringFormat("%02d:%02d", RangeEndHour, RangeEndMinute), " server). Attempting to identify range...");
      IdentifyRange();
      if(RangeHigh > 0 && RangeLow > 0 && RangeHigh > RangeLow) {
         RangeIdentified = true;
         Print("[IMMEDIATE] Range successfully identified: High=", DoubleToString(RangeHigh, Digits), ", Low=", DoubleToString(RangeLow, Digits), ", Size=", DoubleToString(RangeHigh-RangeLow, Digits));
      } else {
         Print("[IMMEDIATE] ERROR: Failed to identify valid range. RangeHigh=", DoubleToString(RangeHigh, Digits), ", RangeLow=", DoubleToString(RangeLow, Digits));
      }
   } else if(currentServerHour < RangeEndHour) {
      Print("[IMMEDIATE] EA loaded before range period ends (", StringFormat("%02d:%02d", currentServerHour, currentServerMin), " < ", StringFormat("%02d:%02d", RangeEndHour, RangeEndMinute), " server). Range will be created at ", StringFormat("%02d:%02d", RangeEndHour, RangeEndMinute), " server.");
   } else if(BreakoutTradesPlacedToday) {
      Print("[IMMEDIATE] Range already processed today. Using existing range values to prevent recalculation.");
   }

   // Determine session trade time
   int tradeHour = 6, tradeMinute = 0; // Default 24hr session
   string sessionName = "24hr";
   switch (TradingSessionPreset) {
      case SESSION_24HR: // 24hr - place at 06:00
         tradeHour = 6; tradeMinute = 0; sessionName = "24hr";
         break;
      case SESSION_LONDON: // London session
         tradeHour = 8; tradeMinute = 0; sessionName = "London";
         break;
      case SESSION_US: // US session
         tradeHour = 14; tradeMinute = 0; sessionName = "US";
         break;
      default:


         tradeHour = 6; tradeMinute = 0; sessionName = "Default";
         break;
   }

   // Check if current time has passed the session trade time
   bool sessionTimePassed = (TimeHour(currentTime) > tradeHour) || (TimeHour(currentTime) == tradeHour && TimeMinute(currentTime) >= tradeMinute);

   Print("[IMMEDIATE] Current server time: ", StringFormat("%02d:%02d", TimeHour(currentTime), TimeMinute(currentTime)), ", Session: ", sessionName, " (", StringFormat("%02d:%02d", tradeHour, tradeMinute), "), Time passed: ", sessionTimePassed);
   Print("[IMMEDIATE] RangeIdentified: ", RangeIdentified, ", BreakoutTradesPlacedToday: ", BreakoutTradesPlacedToday);
   if(RangeIdentified) {
      Print("[IMMEDIATE] Range: High = ", DoubleToString(RangeHigh, Digits), ", Low = ", DoubleToString(RangeLow, Digits));
   }

   // Only place immediate trades if we're past the session time AND range is identified
   if(RangeIdentified && !BreakoutTradesPlacedToday && sessionTimePassed) {
      double currentPrice = iClose(Symbol(), 0, 0);
      Print("[IMMEDIATE] Current price: ", DoubleToString(currentPrice, Digits));
      Print("[IMMEDIATE] Range: ", DoubleToString(RangeLow, Digits), " - ", DoubleToString(RangeHigh, Digits));

      // Validate range before placing trades
      if(RangeHigh <= RangeLow || RangeHigh <= 0 || RangeLow <= 0) {
         Print("[IMMEDIATE] ERROR: Invalid range detected. RangeHigh=", DoubleToString(RangeHigh, Digits), ", RangeLow=", DoubleToString(RangeLow, Digits));
         return;
      }

      // Core late-load logic: place per current price vs range
      if(currentPrice > RangeHigh) {
         Print("[IMMEDIATE] Price above range (", DoubleToString(currentPrice, Digits), " > ", DoubleToString(RangeHigh, Digits), "). Placing SELL ONLY per style.");
         PlacePendingOrders();
         SetBreakoutTradesPlacedToday(true);
      } else if(currentPrice < RangeLow) {
         Print("[IMMEDIATE] Price below range (", DoubleToString(currentPrice, Digits), " < ", DoubleToString(RangeLow, Digits), "). Placing BUY ONLY per style.");
         PlacePendingOrders();
         SetBreakoutTradesPlacedToday(true);
      } else {
         Print("[IMMEDIATE] Price inside range (", DoubleToString(RangeLow, Digits), " <= ", DoubleToString(currentPrice, Digits), " <= ", DoubleToString(RangeHigh, Digits), "). Placing BOTH breakout orders per style.");
         PlacePendingOrders();
         SetBreakoutTradesPlacedToday(true);
      }
   } else if(RangeIdentified && !sessionTimePassed) {
      Print("[IMMEDIATE] Range identified but ", sessionName, " session time (", StringFormat("%02d:%02d", tradeHour, tradeMinute), ") not reached yet. Waiting for session time.");
   } else if(!RangeIdentified) {
      Print("[IMMEDIATE] Range not yet identified. Will wait for range creation at ", StringFormat("%02d:%02d", RangeEndHour, RangeEndMinute), " server time.");
   } else if(BreakoutTradesPlacedToday) {
      Print("[IMMEDIATE] Breakout trades already placed today. No action needed.");
   }
}

//+------------------------------------------------------------------+
//| Helper: Reset Range Times                                        |
//+------------------------------------------------------------------+
void ResetRangeTimes() {
   // Build range using broker server time only (00:00 -> 06:00 server)
   datetime serverToday0 = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   RangeStart = serverToday0 + RangeStartHour * 3600 + RangeStartMinute * 60;   // 00:00 today
   RangeEnd   = serverToday0 + RangeEndHour   * 3600 + RangeEndMinute   * 60;   // 06:00 today
   Print("[DEBUG] ResetRangeTimes (SERVER): Server Range ", TimeToString(RangeStart, TIME_DATE|TIME_MINUTES), " -> ", TimeToString(RangeEnd, TIME_DATE|TIME_MINUTES));
}

//+------------------------------------------------------------------+
//| Helper: Reset Trading Hours                                      |
//+------------------------------------------------------------------+
void ResetTradingHours() {
   // Custom trading hours removed - fakeout uses same session timing as normal trades
   TradingStart = 0;
   TradingEnd = 0;
   Print("[DEBUG] ResetTradingHours: Custom hours disabled - using session presets only");
}

// Fakeout timing functions removed - now uses same session timing as normal trades

//+------------------------------------------------------------------+
//| Helper: Is within custom trading hours                           |
//+------------------------------------------------------------------+
bool IsWithinCustomTradingHours() {
   // Custom trading hours removed - always return true
   return true;
}

//+------------------------------------------------------------------+
//| Helper: Is within trading session                                |
//+------------------------------------------------------------------+
bool IsWithinTradingSession(int hour, int minute) {
   int startHour, endHour, startMinute = 0, endMinute = 59;
   switch(TradingSessionPreset) {
      case SESSION_24HR: // 24hr
         startHour = 0; endHour = 23;
         startMinute = 0; endMinute = 59;
         break;
      case SESSION_LONDON: // London session (8:00-23:50)
         startHour = 8; endHour = 23;
         startMinute = 0; endMinute = 50;
         break;
      case SESSION_US: // US session (14:00-23:50)
         startHour = 14; endHour = 23;
         startMinute = 0; endMinute = 50;
         break;
      default: // Default to 24hr if preset is invalid
         startHour = 0; endHour = 23;
         startMinute = 0; endMinute = 59;
         break;
   }

   if (startHour == endHour) { // Handles sessions that cross midnight or are exactly one hour
       if (startHour == 23 && endHour == 23) return (hour == 23); // 23:00 to 23:59
       if (startHour == 0 && endHour == 0) return (hour == 0); // 00:00 to 00:59
       // For single hour sessions not crossing midnight, standard check works
   }

   if (startHour < endHour) { // Session does not cross midnight
      return (hour > startHour || (hour == startHour && minute >= startMinute)) &&
             (hour < endHour || (hour == endHour && minute <= endMinute));
   } else { // Session crosses midnight (e.g., 22:00 to 04:00)
      return (hour > startHour || (hour == startHour && minute >= startMinute)) ||
             (hour < endHour || (hour == endHour && minute <= endMinute));
   }
}

//+------------------------------------------------------------------+
//| Helper: Is Trading End                                           |
//+------------------------------------------------------------------+
bool IsTradingEnd(int hour, int minute) {
   int endHour = 23, endMinute = 50; // All sessions end at 22:50 server (cleanup starts 22:00)

   switch(TradingSessionPreset) {
      case SESSION_24HR: // 24hr - ends at 23:50
         endHour = 23; endMinute = 50;
         break;
      case SESSION_LONDON: // London - ends at 23:50
         endHour = 23; endMinute = 50;
         break;
      case SESSION_US: // US - ends at 23:50
         endHour = 23; endMinute = 50;
         break;
      default: // All sessions end at 23:50
         endHour = 23; endMinute = 50;
         break;
   }
   return (hour == endHour && minute == endMinute);
}

//+------------------------------------------------------------------+
//| Function to identify the price range                             |
//+------------------------------------------------------------------+
void IdentifyRange() {
   RangeHigh = -DBL_MAX;
   RangeLow = DBL_MAX;
   int barsInRange = 0;

   Print("[DEBUG] IdentifyRange: Searching for range between ", TimeToString(RangeStart, TIME_DATE|TIME_MINUTES),
         " and ", TimeToString(RangeEnd, TIME_DATE|TIME_MINUTES));

   for(int i = 0; i < Bars; i++) {
      datetime barTime = Time[i];
      if(barTime >= RangeStart && barTime < RangeEnd) { // end-exclusive to match indicator (exclude 06:00 bar)
         if(High[i] > RangeHigh) RangeHigh = High[i];
         if(Low[i] < RangeLow) RangeLow = Low[i];
         barsInRange++;
      } else if(barTime < RangeStart) {
         break;
      }
   }

   // Validate range
   if(barsInRange == 0) {
      Print("[WARNING] IdentifyRange: No bars found in range period. Check range times.");
      RangeHigh = 0;
      RangeLow = 0;
      return;
   }

   if(RangeHigh == -DBL_MAX || RangeLow == DBL_MAX) {
      Print("[WARNING] IdentifyRange: Invalid range from chart TF. Falling back to M1 scan.");
      int tf = PERIOD_M1;
      int startIndex = iBarShift(Symbol(), tf, RangeStart, true);
      int endIndex = iBarShift(Symbol(), tf, RangeEnd, true);
      if(startIndex < 0) startIndex = iBars(Symbol(), tf) - 1;
      if(endIndex < 0) endIndex = 0;
      int m1count = 0;
      RangeHigh = -DBL_MAX;
      RangeLow = DBL_MAX;
      for(int j = startIndex; j >= endIndex; j--) {
         datetime t = iTime(Symbol(), tf, j);
         if(t < RangeStart || t >= RangeEnd) continue; // enforce [start, end)
         double hi = iHigh(Symbol(), tf, j);
         double lo = iLow(Symbol(), tf, j);
         if(hi > RangeHigh) RangeHigh = hi;
         if(lo < RangeLow) RangeLow = lo;
         m1count++;
      }
      if(m1count == 0) {
         Print("[ERROR] IdentifyRange: No M1 bars in range window; cannot compute.");
         RangeHigh = 0;
         RangeLow = 0;
         return;
      }
      barsInRange = m1count;
   }
   // Range Mode handling
   if(RangeMode == RANGE_DIFFERENT_2345_0600) {
      int tf = PERIOD_M1;
      datetime today0 = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
      datetime start = today0;                                // 00:00
      datetime startPre = today0 - 15*60;                     // 23:45 previous day
      datetime end   = today0 + RangeEndHour*3600;            // 06:00

      // Scan pre-midnight segment [23:45, 00:00)
      int idxPreStart = iBarShift(Symbol(), tf, startPre, true);
      int idxPreEnd   = iBarShift(Symbol(), tf, start, true);
      if(idxPreStart < 0) idxPreStart = iBars(Symbol(), tf) - 1;
      if(idxPreEnd < 0) idxPreEnd = 0;

      RangeHigh = -DBL_MAX;
      RangeLow = DBL_MAX;
      int count = 0;
      for(int j = idxPreStart; j >= idxPreEnd; j--) {
         datetime t = iTime(Symbol(), tf, j);
         if(t < startPre || t >= start) continue;
         double hi = iHigh(Symbol(), tf, j);
         double lo = iLow(Symbol(), tf, j);
         if(hi > RangeHigh) RangeHigh = hi;
         if(lo < RangeLow) RangeLow = lo;
         count++;
      }

      // Scan main segment [00:00, 06:00)
      int idxStart = iBarShift(Symbol(), tf, start, true);
      int idxEnd   = iBarShift(Symbol(), tf, end, true);
      if(idxStart < 0) idxStart = iBars(Symbol(), tf) - 1;
      if(idxEnd < 0) idxEnd = 0;

      for(int k = idxStart; k >= idxEnd; k--) {
         datetime t2 = iTime(Symbol(), tf, k);
         if(t2 < start || t2 >= end) continue;
         double hi2 = iHigh(Symbol(), tf, k);
         double lo2 = iLow(Symbol(), tf, k);
         if(hi2 > RangeHigh) RangeHigh = hi2;
         if(lo2 < RangeLow) RangeLow = lo2;
         count++;
      }

      barsInRange = count;
      Print("[RANGE MODE] 23:45–06:00 range. Bars=", count,
            " | Pre-midnight start=", TimeToString(startPre, TIME_MINUTES),
            " 00:00=", TimeToString(start, TIME_MINUTES),
            " End=", TimeToString(end, TIME_MINUTES));
   }

   Print("[DEBUG] IdentifyRange: Found ", barsInRange, " bars in range period");
   // Calculate ADR (Average Daily Range)
   double adrSum = 0;
   int adrCount = 0;
   if (ADRType == 0) {
      // Daily ADR (default)
      for(int d = 1; d <= ADRDays; d++) {
         double dayHigh = -DBL_MAX, dayLow = DBL_MAX;
         datetime dayStart = RangeStart - d*86400;
         datetime dayEnd = RangeEnd - d*86400;
         for(int iBar = 0; iBar < Bars; iBar++) {
            datetime barTime = Time[iBar];
            if(barTime >= dayStart && barTime <= dayEnd) {
               if(High[iBar] > dayHigh) dayHigh = High[iBar];
               if(Low[iBar] < dayLow) dayLow = Low[iBar];
            } else if(barTime < dayStart) {
               break;
            }
         }
         if(dayHigh > -DBL_MAX && dayLow < DBL_MAX) {
            adrSum += MathAbs(dayHigh - dayLow);
            adrCount++;
         }
      }
      CurrentADR = (adrCount > 0) ? adrSum / adrCount : 0;
   } else {
      // Weekly ADR: average weekly range / 5
      int weeks = MathMax(1, ADRDays/5);
      for(int w = 1; w <= weeks; w++) {
         double weekHigh = -DBL_MAX, weekLow = DBL_MAX;
         datetime weekStart = RangeStart - w*7*86400;
         datetime weekEnd = RangeEnd - (w-1)*7*86400;
         for(int iBar = 0; iBar < Bars; iBar++) {
            datetime barTime = Time[iBar];
            if(barTime >= weekStart && barTime <= weekEnd) {
               if(High[iBar] > weekHigh) weekHigh = High[iBar];
               if(Low[iBar] < weekLow) weekLow = Low[iBar];
            } else if(barTime < weekStart) {
               break;
            }
         }
         if(weekHigh > -DBL_MAX && weekLow < DBL_MAX) {
            adrSum += MathAbs(weekHigh - weekLow) / 5.0;
            adrCount++;
         }
      }
      CurrentADR = (adrCount > 0) ? adrSum / adrCount : 0;
   }
   double todayRange = MathAbs(RangeHigh - RangeLow);
   ADRTooBig = (UseADRLimit && CurrentADR > 0 && todayRange > MaxRangeADRMultiple * CurrentADR);
   DrawRangeVisuals();
   Print("Range identified: High = "+DoubleToString(RangeHigh,2)+", Low = "+DoubleToString(RangeLow,2)+". ADR="+DoubleToString(CurrentADR,2)+". ADRTooBig="+(ADRTooBig?"true":"false"));
}

//+------------------------------------------------------------------+
//| Draw range box and lines                                         |
//+------------------------------------------------------------------+
void DrawRangeVisuals() {
   DeleteRangeVisuals();
   color boxColors[7] = {clrAqua, clrOrange, clrViolet, clrLightSeaGreen, clrYellow, clrPink, clrGray}; // Mon-Sun
   color topColors[7] = {clrBlue, clrRed, clrMagenta, clrGreen, clrRed, clrPurple, clrSlateGray};
   color bottomColors[7] = {clrBlue, clrRed, clrMagenta, clrGreen, clrLime, clrPurple, clrSlateGray};
   // Use the same computed RangeStart/RangeEnd (server time) used by IdentifyRange()
   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   int todayDOW = TimeDayOfWeek(today);
   string dayStr = TimeToString(today, TIME_DATE);

   datetime rngStart = RangeStart;
   datetime rngEnd   = RangeEnd;

   // Compute hi/lo over the exact range window used for trading
   double hi = -DBL_MAX, lo = DBL_MAX;
   for(int i=0; i<Bars; i++) {
      datetime barTime = Time[i];
      if(barTime >= rngStart && barTime <= rngEnd) {
         if(High[i] > hi) hi = High[i];
         if(Low[i] < lo)  lo = Low[i];
      } else if(barTime < rngStart) {
         break;
      }
   }
   if(hi == -DBL_MAX || lo == DBL_MAX) return; // No data

   int colorIdx = (todayDOW-1+7)%7;
   color boxCol = boxColors[colorIdx];
   string boxName = "RangeBox_" + dayStr;

   // Draw rectangle exactly over the computed range window
   ObjectCreate(0, boxName, OBJ_RECTANGLE, 0, rngStart, hi, rngEnd, lo);
   ObjectSetInteger(0, boxName, OBJPROP_COLOR, boxCol);
   ObjectSetInteger(0, boxName, OBJPROP_BACK, true);
   ObjectSetInteger(0, boxName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, boxName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, boxName, OBJPROP_FILL, true);
}


//+------------------------------------------------------------------+
//| Delete range visuals                                             |
//+------------------------------------------------------------------+
void DeleteRangeVisuals() {
   ObjectsDeleteAll(0, "RangeBox_");
   ObjectsDeleteAll(0, "RangeTop_");
   ObjectsDeleteAll(0, "RangeBottom_");
}

//+------------------------------------------------------------------+
//| Place pending orders                                             |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| FAKEOUT REVERSAL (Fade) LOGIC                                   |
//+------------------------------------------------------------------+
bool fakeoutUp = false, fakeoutDown = false;
bool fakeoutChecked = false;
// Track any pre-session breakouts of the daily range (independent of fakeout feature)
bool preBreakUp = false, preBreakDown = false;

void CheckFakeoutBeforeSession() {
   // Always track pre-session breakouts regardless of fakeout feature
   // Only use fakeout flags for fade entries when explicitly enabled
   if(fakeoutChecked) {
      // Already processed for this session day
      return;
   }

   // Determine session open time based on TradingSessionPreset
   int sessionHour = 6, sessionMinute = 0; // Default 24hr session
   switch (TradingSessionPreset) {
      case SESSION_24HR: // 24hr - session starts at 06:00
         sessionHour = 6; sessionMinute = 0;
         break;
      case SESSION_LONDON: // London session starts at 08:00
         sessionHour = 8; sessionMinute = 0;
         break;
      case SESSION_US: // US session starts at 14:00
         sessionHour = 14; sessionMinute = 0;
         break;
      default:
         sessionHour = 6; sessionMinute = 0;
         break;
   }

   datetime sessionOpen = StrToTime(TimeToStr(TimeCurrent(), TIME_DATE) + " " + IntegerToString(sessionHour,2,'0') + ":" + IntegerToString(sessionMinute,2,'0'));
   if(TimeCurrent() < sessionOpen) {
      double price = iClose(Symbol(), 0, 0);
      if(price > RangeHigh) { preBreakUp = true; if(EnableFakeoutFade) fakeoutUp = true; }
      if(price < RangeLow)  { preBreakDown = true; if(EnableFakeoutFade) fakeoutDown = true; }
   }
   if(TimeCurrent() >= sessionOpen) fakeoutChecked = true;
}

//+------------------------------------------------------------------+
//| Enhanced Fakeout Logic                                           |
//+------------------------------------------------------------------+
void CheckEnhancedFakeout() {
   if(!EnableEnhancedFakeout || !RangeIdentified) {
      FakeoutLogicActive = false;
      return;
   }

   // CRITICAL: Check risk limits before any fakeout trading
   if(TradingPermanentlyStopped || DayLossLimitHit || WeekLossLimitHit || TotalLossLimitHit) {
      Print("[FAKEOUT] Risk limits hit. No fakeout trades allowed.");
      FakeoutLogicActive = false;
      return;
   }

   // Fakeout logic now uses same session timing as normal trades
   FakeoutLogicActive = true;

   double currentPrice = iClose(Symbol(), 0, 0);
   double buffer = MarketInfo(Symbol(), MODE_SPREAD) * Point;

   // Enhanced fakeout logic: Place SELL orders when price breaks above range high
   if(currentPrice > RangeHigh && !FakeoutSellOrderPlaced) {
      // Calculate lot size using the same logic as regular trades
      double useLot = ActualLotSize1;
      if(useLot <= 0) {
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
      }

      // Calculate SL and TP for fakeout sell order
      double sellPrice = RangeHigh;  // SELL at range high, not current market price
      double sellSL = 0, sellTP = 0;

      // Use same SL calculation as regular trades
      switch(StopLossMethod) {
         case SL_RANGE:
            sellSL = RangeHigh + (AddedStopLoss * Point);
            break;
         case SL_POINTS:
            sellSL = sellPrice + (FixedStopLossPoints * Point);
            break;
         case SL_PRICE:
            sellSL = FixedStopLossPrice;
            break;
         case SL_MIDRANGE:
            {
               double rangeMid = (RangeHigh + RangeLow) / 2.0;
               sellSL = rangeMid;
            }
            break;
      }

      // Calculate TP
      if(TradeTargetPoints > 0) {
         sellTP = sellPrice - TradeTargetPoints * Point;
      } else if(TradeTargetR > 0) {
         double rangeSize = MathAbs(RangeHigh - RangeLow);
         sellTP = sellPrice - TradeTargetR * rangeSize;
      }

      // Check if fakeout sell order doesn't already exist
      int fakeoutSellMagic = MagicBase + 6000; // Unique magic for fakeout sells
      bool fakeoutSellExists = false;
      for(int i = 0; i < OrdersTotal(); i++) {
         if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == fakeoutSellMagic) {
               fakeoutSellExists = true;
               break;
            }
         }
      }

      if(!fakeoutSellExists) {
         // FIXED: Place SELL LIMIT at range high, not market order at current price
         if(RobustOrderSend(Symbol(), OP_SELLLIMIT, useLot, sellPrice, 3, sellSL, sellTP, "Enhanced Fakeout Sell", fakeoutSellMagic, 0, clrRed)) {
            FakeoutSellOrderPlaced = true;
            Print("[ENHANCED FAKEOUT] SELL LIMIT placed at range high: ", DoubleToString(sellPrice, Digits), " (current price: ", DoubleToString(currentPrice, Digits), ")");
         }
      }
   }

   // Enhanced fakeout logic: Place BUY orders when price breaks below range low
   if(currentPrice < RangeLow && !FakeoutBuyOrderPlaced) {
      // Calculate lot size using the same logic as regular trades
      double useLot = ActualLotSize1;
      if(useLot <= 0) {
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
      }

      // Calculate SL and TP for fakeout buy order
      double buyPrice = RangeLow;  // BUY at range low, not current market price
      double buySL = 0, buyTP = 0;

      // Use same SL calculation as regular trades
      switch(StopLossMethod) {
         case SL_RANGE:
            buySL = RangeLow - (AddedStopLoss * Point);
            break;
         case SL_POINTS:
            buySL = buyPrice - (FixedStopLossPoints * Point);
            break;
         case SL_PRICE:
            buySL = FixedStopLossPrice;
            break;
         case SL_MIDRANGE:
            {
               double rangeMid = (RangeHigh + RangeLow) / 2.0;
               buySL = rangeMid;
            }
            break;
      }

      // Calculate TP
      if(TradeTargetPoints > 0) {
         buyTP = buyPrice + TradeTargetPoints * Point;
      } else if(TradeTargetR > 0) {
         double rangeSize = MathAbs(RangeHigh - RangeLow);
         buyTP = buyPrice + TradeTargetR * rangeSize;
      }

      // Check if fakeout buy order doesn't already exist
      int fakeoutBuyMagic = MagicBase + 5000; // Unique magic for fakeout buys
      bool fakeoutBuyExists = false;
      for(int i = 0; i < OrdersTotal(); i++) {
         if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == fakeoutBuyMagic) {
               fakeoutBuyExists = true;
               break;
            }
         }
      }

      if(!fakeoutBuyExists) {
         // FIXED: Place BUY LIMIT at range low, not market order at current price
         if (RobustOrderSend(Symbol(), OP_BUYLIMIT, useLot, buyPrice, 3, buySL, buyTP, "Enhanced Fakeout Buy", fakeoutBuyMagic, 0, clrGreen)) {
            FakeoutBuyOrderPlaced = true;
            Print("[ENHANCED FAKEOUT] BUY LIMIT placed at range low: ", DoubleToString(buyPrice, Digits), " (current price: ", DoubleToString(currentPrice, Digits), ")");
         }
      }
   }
}

bool OpenPositionExists(int type, int magic) {
    datetime today0 = StringToTime(TimeToString(TimeCurrent(), TIME_DATE)); // server midnight
    for(int i=0; i<OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderSymbol()==Symbol() && OrderMagicNumber()==magic && (OrderType()==OP_BUY||OrderType()==OP_SELL)) {
            if(OrderOpenTime() >= today0) {
                if((type==OP_BUYSTOP && OrderType()==OP_BUY) || (type==OP_SELLSTOP && OrderType()==OP_SELL)) return true;
            }
        }
    }
    return false;
}

bool PendingOrderExists(int type, int magic, double price, double sl, double tp, double lot, double priceTol=0) {
    if(priceTol == 0) priceTol = 10*Point;
    for(int i=0; i<OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderSymbol()==Symbol() && OrderMagicNumber()==magic && OrderType()==type) {
            if(MathAbs(OrderOpenPrice()-price)<=priceTol && MathAbs(OrderLots()-lot)<1e-6 && MathAbs(OrderStopLoss()-sl)<priceTol && MathAbs(OrderTakeProfit()-tp)<priceTol)
                return true;
        }
    }
    return false;
}

void PlacePendingOrders() {
   Print("[DEBUG] PlacePendingOrders() called - TradingSessionPreset: ", TradingSessionPreset);
   Print("[DEBUG] BreakoutTradesPlacedToday = ", BreakoutTradesPlacedToday);

   // RISK MANAGEMENT REMOVED - TRADES WILL BE PLACED
   Print("[TRADE] Proceeding with trade placement - no risk blocks");

   // OPTIMAL FIX: Only allow trade placement ONCE per day
   if(BreakoutTradesPlacedToday) {
      Print("[OPTIMAL] Trades already placed today. No more trade placement allowed.");
      return;
   }

   // ADDITIONAL SAFETY: Check if any breakout orders already exist today
   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE)); // server midnight
   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() &&
            OrderMagicNumber() >= MagicBase + 2000 && OrderMagicNumber() <= MagicBase + 4999 &&
            OrderOpenTime() >= today &&
            (OrderType() == OP_BUYSTOP || OrderType() == OP_SELLSTOP)) {
            Print("[SAFETY] Existing breakout order found from today (Ticket: ", OrderTicket(), "). Preventing duplicate trades.");
            SetBreakoutTradesPlacedToday(true);
            return;
         }
      }
   }

   // Custom trading hours removed - fakeout uses same session timing as normal trades

   // NEW: Weekend filtering logic - Only crypto trades on weekends, all others skip weekends
   datetime currentTime = TimeCurrent();
   int dayOfWeek = TimeDayOfWeek(currentTime); // 0=Sunday, 1=Monday, ..., 6=Saturday
   int assetType = GetAssetType(); // 0=Forex, 1=Indices/Metals/Crypto

   // Check if it's weekend
   if(dayOfWeek == 0 || dayOfWeek == 6) {
      // Only allow crypto trading on weekends
      // Use local StringToUpper implementation
      string sym = Symbol();
      string symUpper = "";
      for(int i = 0; i < StringLen(sym); i++) {
          ushort ch = (ushort)StringGetCharacter(sym, i);
          if(ch >= 97 && ch <= 122) ch = ch - 32;
          string charStr = CharToString((char)ch);
          StringConcatenate(symUpper, symUpper, charStr);
      }
      bool isCrypto = (StringFind(symUpper, "BTC") >= 0 || StringFind(symUpper, "ETH") >= 0 || StringFind(symUpper, "LTC") >= 0);

      if(!isCrypto) {
         Print("[DEBUG] Skipping non-crypto trade placement on weekend (day ", IntegerToString(dayOfWeek), "). Only crypto trades on weekends.");
         return;
      } else {
         Print("[DEBUG] Crypto detected - trading allowed on weekend (day ", IntegerToString(dayOfWeek), ").");
      }
   } else {
      Print("[DEBUG] Weekday trading (day ", IntegerToString(dayOfWeek), ") - all assets allowed.");
   }

   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double lot = AlwaysUseMinLot ? minLot : MathMax(LotSize, minLot);
   double spread = MarketInfo(Symbol(), MODE_SPREAD) * Point;
   double atr = 0;
   if(EnableATRFilter) atr = iATR(Symbol(), 0, ATRPeriod, 0);

   // Range size filters
   double rangeSize = MathAbs(RangeHigh - RangeLow);
   double rangeSizePoints = rangeSize / Point;
   bool validRange = true;

   // ATR/volatility-based range filter
   if(EnableATRFilter && atr > 0) {
      double minRange = MinRangeATR * atr;
      double maxRange = MaxRangeATR * atr;
      if(rangeSize < minRange || rangeSize > maxRange) {
         Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSize,2), " outside ATR bounds (", DoubleToString(minRange,2), "-", DoubleToString(maxRange,2), ")");
         validRange = false;
      }
   }

   // Dynamic range filter (Min/Max range points)
   if(MinRangePoints > 0 && rangeSizePoints < MinRangePoints) {
      Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSizePoints,1), " points below minimum (", DoubleToString(MinRangePoints,1), " points)");
      validRange = false;
   }
   if(MaxRangePoints > 0 && rangeSizePoints > MaxRangePoints) {
      Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSizePoints,1), " points above maximum (", DoubleToString(MaxRangePoints,1), " points)");
      validRange = false;
   }

   if(!validRange) {
      Print("[DEBUG] Skipping trade placement: invalid range");
      return;
   }

   // Dynamic entry buffer
   double buffer = 0;
   if(EnableDynamicEntryBuffer) {
      if(EntryBufferPoints > 0) buffer = EntryBufferPoints * Point;
      else if(EntryBufferPercent > 0) buffer = rangeSize * EntryBufferPercent;
   } else {
      buffer = spread;
   }

   // Check current price position relative to range - use most accurate current price
   double currentBid = Bid;
   double currentAsk = Ask;
   double currentPrice = (currentBid + currentAsk) / 2; // Use mid-price for more accurate range detection

   // Add buffer for price comparison to avoid edge cases
   double priceBuffer = 2 * Point; // 2 point buffer

   bool priceAboveRange = (currentPrice > (RangeHigh + priceBuffer));
   bool priceBelowRange = (currentPrice < (RangeLow - priceBuffer));
   bool priceInsideRange = (currentPrice >= (RangeLow - priceBuffer) && currentPrice <= (RangeHigh + priceBuffer));

   Print("[CRITICAL] PlacePendingOrders: Current Bid = ", DoubleToString(currentBid, Digits), ", Ask = ", DoubleToString(currentAsk, Digits), ", Mid = ", DoubleToString(currentPrice, Digits));
   Print("[CRITICAL] PlacePendingOrders: RangeHigh = ", DoubleToString(RangeHigh, Digits), ", RangeLow = ", DoubleToString(RangeLow, Digits));
   Print("[CRITICAL] PlacePendingOrders: priceAboveRange = ", priceAboveRange, ", priceBelowRange = ", priceBelowRange, ", priceInsideRange = ", priceInsideRange);

   // CRITICAL VALIDATION: If price is significantly outside range, don't place trades
   // Use existing rangeSize variable declared earlier
   double priceDistanceFromRange = 0;
   if(priceAboveRange) {
      priceDistanceFromRange = currentPrice - RangeHigh;
   } else if(priceBelowRange) {
      priceDistanceFromRange = RangeLow - currentPrice;
   }

   if(priceDistanceFromRange > rangeSize * 0.5) { // If price is more than 50% of range size away
      Print("[CRITICAL] Price too far from range (", DoubleToString(priceDistanceFromRange, Digits), " vs range size ", DoubleToString(rangeSize, Digits), "). Skipping trade placement to prevent invalid orders.");
      return;
   }

   // For range breakout strategy, stop loss should be at range boundaries
   // NOT at historical extremes from previous days
   double dayHigh = RangeHigh;  // Use current day's range high
   double dayLow = RangeLow;    // Use current day's range low

   Print("[DEBUG] Using range boundaries for SL: dayHigh = ", DoubleToString(dayHigh, Digits), ", dayLow = ", DoubleToString(dayLow, Digits));

   // STRICT RULE: ONLY place trades at exact range boundaries - NO EXCEPTIONS
   double buyStop = 0, sellStop = 0;

   // ALWAYS place BUY STOP at RangeHigh and SELL STOP at RangeLow
   // This is a range breakout strategy - trades MUST be at range boundaries
   buyStop = RangeHigh;   // BUY STOP always at range high
   sellStop = RangeLow;   // SELL STOP always at range low

   Print("[STRICT] Range Breakout Strategy: ALWAYS placing trades at exact range boundaries:");
   Print("[STRICT] BUY STOP at RangeHigh: ", DoubleToString(buyStop, Digits));
   Print("[STRICT] SELL STOP at RangeLow: ", DoubleToString(sellStop, Digits));
   Print("[STRICT] Current price position is irrelevant - we trade breakouts from range boundaries only");

   // Validate that range boundaries are valid
   if(RangeHigh <= RangeLow) {
      Print("[ERROR] Invalid range: RangeHigh (", DoubleToString(RangeHigh, Digits), ") <= RangeLow (", DoubleToString(RangeLow, Digits), "). Cannot place trades.");
      return;
   }

   // Validate minimum range size (use MinRangePoints setting)
   double minRangeSize = (MinRangePoints > 0) ? MinRangePoints * Point : 10 * Point;
   if(rangeSize < minRangeSize) {
      Print("[ERROR] Range too small (", DoubleToString(rangeSize, Digits), "). Minimum ", DoubleToString(MinRangePoints, 0), " points required. Cannot place trades.");
      return;
   }

   // --- Fakeout Reversal Logic ---
   // Determine session open time based on TradingSessionPreset
   int sessionHour = 6, sessionMinute = 0; // Default 24hr session
   switch (TradingSessionPreset) {
      case SESSION_24HR: // 24hr - session starts at 06:00
         sessionHour = 6; sessionMinute = 0;
         break;
      case SESSION_LONDON: // London session starts at 08:00
         sessionHour = 8; sessionMinute = 0;
         break;
      case SESSION_US: // US session starts at 14:00
         sessionHour = 14; sessionMinute = 0;
         break;
      default:
         sessionHour = 6; sessionMinute = 0;
         break;
   }

   datetime sessionOpen = StrToTime(TimeToStr(TimeCurrent(), TIME_DATE) + " " + IntegerToString(sessionHour,2,'0') + ":" + IntegerToString(sessionMinute,2,'0'));
   bool fadeSell = false, fadeBuy = false;
   if(EnableFakeoutFade && TimeCurrent() >= sessionOpen) {
      if(fakeoutUp) fadeSell = true;
      if(fakeoutDown) fadeBuy = true;
   }

   // --- ONLY PLACE ORDERS IN THE DIRECTION THAT HAS NOT ALREADY BROKEN OUT ---
   double lastPrice = iClose(Symbol(), 0, 0);
   bool allowBuy = false;
   bool allowSell = false;

   // AUTO-ENABLE FAKE OUT LOGIC: If range broke out before session time and no trades placed yet
   bool autoFakeoutActive = false;
   if(EnableFakeoutFade && TimeCurrent() >= sessionOpen && !BreakoutTradesPlacedToday) {
      if(lastPrice > RangeHigh || lastPrice < RangeLow) {
         autoFakeoutActive = true;
         Print("[DEBUG] AUTO-FAKEOUT: Range broke out before session time. Auto-enabling fakeout logic.");
         if(lastPrice > RangeHigh) {
            fakeoutUp = true;
            Print("[DEBUG] AUTO-FAKEOUT: Price above range - setting fakeoutUp = true");
         }
         if(lastPrice < RangeLow) {
            fakeoutDown = true;
            Print("[DEBUG] AUTO-FAKEOUT: Price below range - setting fakeoutDown = true");
         }
      } else {
         Print("[DEBUG] Price still inside range at session time. Using normal breakout logic.");
      }
   }

   // Apply fake out logic only when explicitly enabled
   if(EnableFakeoutFade && TimeCurrent() >= sessionOpen) {
      if(fakeoutUp && fakeoutDown) {
         // Both directions broke out - price is very volatile, use normal breakout logic
         if(lastPrice <= RangeHigh && lastPrice >= RangeLow) {
            allowBuy = true;
            allowSell = true;
            Print("[DEBUG] BOTH fakeouts detected but price back inside range. Using normal breakout logic due to high volatility.");
         } else if(lastPrice > RangeHigh) {
            allowSell = true;
            allowBuy = false;
            Print("[DEBUG] BOTH fakeouts detected, price above range. Only SELL order.");
         } else if(lastPrice < RangeLow) {
            allowBuy = true;
            allowSell = false;
            Print("[DEBUG] BOTH fakeouts detected, price below range. Only BUY order.");
         }
      } else if(fakeoutUp && !fakeoutDown) {
         // Only upward fakeout - fade it with SELL
         allowSell = true;
         allowBuy = false;
         Print("[DEBUG] Fakeout UP detected: Only placing SELL to fade the upward breakout.");
      } else if(fakeoutDown && !fakeoutUp) {
         // Only downward fakeout - fade it with BUY
         allowBuy = true;
         allowSell = false;
         Print("[DEBUG] Fakeout DOWN detected: Only placing BUY to fade the downward breakout.");
      } else {
         // No fakeout detected, use normal breakout logic
         if(!EnableReversalEntryLogic) {
            // If price is inside the range, allow both normal breakout orders
            if(lastPrice <= RangeHigh && lastPrice >= RangeLow) {
               allowBuy = true;
               allowSell = true;
               Print("[DEBUG] Price inside range. Placing normal breakout orders: BUY STOP above range, SELL STOP below range.");
            } else if(lastPrice > RangeHigh) {
               // Price already broke out to the upside, only allow sell
               allowBuy = false;
               allowSell = true;
               Print("[DEBUG] Price above range. Only allowing SELL order.");
            } else if(lastPrice < RangeLow) {
               // Price already broke out to the downside, only allow buy
               allowBuy = true;
               allowSell = false;
               Print("[DEBUG] Price below range. Only allowing BUY order.");
            }
         } else {
            // In reversal mode, allow both
            allowBuy = true;
            allowSell = true;
            Print("[DEBUG] Reversal mode enabled. Allowing both BUY and SELL orders.");
         }
      }
   } else {
      // Fake out logic not enabled and no auto-fakeout, use normal logic
      Print("[DEBUG] No fakeout logic active. Using normal breakout logic.");
      if(!EnableReversalEntryLogic) {
         // If price is inside the range, allow both normal breakout orders
         if(lastPrice <= RangeHigh && lastPrice >= RangeLow) {
            allowBuy = true;
            allowSell = true;
            Print("[DEBUG] Price inside range. Placing normal breakout orders: BUY STOP above range, SELL STOP below range.");
         } else if(lastPrice > RangeHigh) {
            // Price already broke out to the upside, only allow sell
            allowBuy = false;
            allowSell = true;
            Print("[DEBUG] Price above range. Only allowing SELL order.");
         } else if(lastPrice < RangeLow) {
            // Price already broke out to the downside, only allow buy
            allowBuy = true;
            allowSell = false;
            Print("[DEBUG] Price below range. Only allowing BUY order.");
         }
      } else {
         // In reversal mode, allow both
         allowBuy = true;
         allowSell = true;
         Print("[DEBUG] Reversal mode enabled. Allowing both BUY and SELL orders.");
      }
   }

   Print("[DEBUG] Final decision: allowBuy = ", allowBuy, ", allowSell = ", allowSell);

   // SAFETY FUSE: ALWAYS enforce range breakout logic regardless of fakeout settings
   // - If price inside range: place both BUY STOP at high and SELL STOP at low
   // - If price broke down: only BUY STOP at RangeHigh (reversal trade)
   // - If price broke up: only SELL STOP at RangeLow (reversal trade)
   double cp_enforce = Bid;
   bool insideRange = (cp_enforce >= RangeLow && cp_enforce <= RangeHigh);

   // CRITICAL FIX: Always run safety fuse regardless of EnableFakeoutFade setting
   if(insideRange) {
      allowBuy = true; allowSell = true;
      buyStop = RangeHigh; sellStop = RangeLow;
      Print("[SAFETY] Price inside range - allowing both BUY STOP and SELL STOP");
   } else if(cp_enforce < RangeLow) {
      allowBuy = true; allowSell = false;
      buyStop = RangeHigh; sellStop = 0;
      Print("[SAFETY] Price broke DOWN - only allowing BUY STOP at range high");
   } else if(cp_enforce > RangeHigh) {
      allowBuy = false; allowSell = true;
      sellStop = RangeLow; buyStop = 0;
      Print("[SAFETY] Price broke UP - only allowing SELL STOP at range low");
   }
   Print("[DEBUG] After safety fuse: insideRange=", insideRange, ", allowBuy=", allowBuy, ", allowSell=", allowSell,
         ", buyStop=", DoubleToString(buyStop, Digits), ", sellStop=", DoubleToString(sellStop, Digits));

   // Enforce exactly one trade placement per day
   int effectiveNumTrades = 1; // hard limit to 1 per side as per spec
   bool buyPlaced = false;
   bool sellPlaced = false;



   for(int t=1; t<=effectiveNumTrades; t++) {
      // Per-trade settings
      double useLot = 0;
      if (t == 1) useLot = ActualLotSize1;
      else if (t == 2) useLot = LotSize2;
      else if (t >= 3) useLot = LotSize3;
      else useLot = LotSize1;
      if(useLot <= 0) {
         // Fallback: use risk-based or broker minimum if user input is not valid
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else if(AlwaysUseMinLot)
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
         else
            useLot = 0.01; // Final fallback
      }
      double tpInput = TP1; int slInput = SL1, ruleInput = Trade1Rule; string commentInput = Comment1; color tradeColor = Trade1Color;
      if(t == 2) { tpInput = TP2; slInput = SL2; ruleInput = Trade2Rule; commentInput = Comment2; tradeColor = Trade2Color; }
      else if(t >= 3) { tpInput = TP3; slInput = SL3; ruleInput = Trade3Rule; commentInput = Comment3; tradeColor = Trade3Color; }
      TradeRuleType rule = (ruleInput == 1) ? RuleTightSL : (ruleInput == 2) ? RuleBreakevenRunner : RuleNormal;
      double buySL = 0, sellSL = 0;
      string buyComment = commentInput, sellComment = commentInput;
      int buyMagic = MagicBase + 2000 + t, sellMagic = MagicBase + 4000 + t;
      color buyColor = tradeColor, sellColor = tradeColor;
      // CRITICAL FIX: ALWAYS place orders at exact range boundaries - NO staggered orders allowed
      // Range breakout strategy requires precise placement at range high/low
      double buyStopLevel = NormalizeDouble(RangeHigh, Digits);  // ALWAYS at range high - exact tick
      double sellStopLevel = NormalizeDouble(RangeLow, Digits);   // ALWAYS at range low - exact tick

      // Validate order placement levels
      if(buyStopLevel != RangeHigh) {
         Print("[ERROR] BUY STOP level (", DoubleToString(buyStopLevel, Digits), ") != RangeHigh (", DoubleToString(RangeHigh, Digits), "). CRITICAL ERROR!");
         return;
      }
      if(sellStopLevel != RangeLow) {
         Print("[ERROR] SELL STOP level (", DoubleToString(sellStopLevel, Digits), ") != RangeLow (", DoubleToString(RangeLow, Digits), "). CRITICAL ERROR!");
         return;
      }

      Print("[VALIDATION] Order levels validated: BUY STOP at ", DoubleToString(buyStopLevel, Digits), " (RangeHigh), SELL STOP at ", DoubleToString(sellStopLevel, Digits), " (RangeLow)");

      // Compute Stop Loss based on selected StopLossMethod
      double calculatedBuySL = RangeLow;   // default fallback
      double calculatedSellSL = RangeHigh; // default fallback
      switch(StopLossMethod) {
         case SL_RANGE:
            // EXACT opposite boundary for range method (no extra buffer)
            calculatedBuySL = RangeLow;
            calculatedSellSL = RangeHigh;
            break;
         case SL_POINTS:
            calculatedBuySL = buyStopLevel - (FixedStopLossPoints * Point);
            calculatedSellSL = sellStopLevel + (FixedStopLossPoints * Point);
            break;
         case SL_PRICE:
            calculatedBuySL = FixedStopLossPrice;
            calculatedSellSL = FixedStopLossPrice;
            break;
         case SL_MIDRANGE:
            {
               double rangeMid = (RangeHigh + RangeLow) / 2.0;
               calculatedBuySL = rangeMid;
               calculatedSellSL = rangeMid;
            }
            break;
      }

      Print("[SL] Computed Stop Losses:");
      Print("[SL] BUY STOP at ", DoubleToString(buyStopLevel, Digits), " (RangeHigh) with SL at ", DoubleToString(calculatedBuySL, Digits));
      Print("[SL] SELL STOP at ", DoubleToString(sellStopLevel, Digits), " (RangeLow) with SL at ", DoubleToString(calculatedSellSL, Digits));

      double buyTP = 0, sellTP = 0;
      if(tpInput > 0) {
         buyTP = buyStopLevel + tpInput*Point;
         sellTP = sellStopLevel - tpInput*Point;
      } else if(TradeTargetPoints > 0) {
         buyTP = buyStopLevel + TradeTargetPoints*Point;
         sellTP = sellStopLevel - TradeTargetPoints*Point;
      } else if(TradeTargetR > 0) {
         buyTP = buyStopLevel + TradeTargetR*rangeSize;
         sellTP = sellStopLevel - TradeTargetR*rangeSize;
      }
      // Always use STOP pending orders at exact range boundaries (no LIMITs)
      int buyOrderType = OP_BUYSTOP;
      string buyOrderTypeName = "BUY STOP";
      buyComment = (!priceInsideRange) ? "Range Broken BUY STOP" : "Normal Breakout BUY STOP";

      // Check for existing open buy position for this slot
      if(!allowBuy || buyStop <= 0) {
          Print("[DEBUG] Buy trades not allowed (allowBuy=", allowBuy, ") or buyStop is 0 (buyStop=", DoubleToString(buyStop, Digits), ")");
      } else if(OpenPositionExists(buyOrderType, buyMagic)) {
          Print("[DEBUG] Open BUY position exists for slot ", IntegerToString(t), ". Not placing new pending buy order.");
      } else if(!PendingOrderExists(buyOrderType, buyMagic, buyStopLevel, calculatedBuySL, buyTP, useLot)) {
          Print("[DEBUG] Sending ", buyOrderTypeName, " order for slot ", IntegerToString(t), " at price ", DoubleToString(buyStopLevel,Digits));
          int orderTicket = OrderSend(Symbol(), buyOrderType, useLot, buyStopLevel, 3, calculatedBuySL, buyTP, buyComment, buyMagic, 0, buyColor);
          if (orderTicket < 0) {
              int err = GetLastError();
              Print("[ERROR] Failed to place ", buyOrderTypeName, " order. Error code: ", err);
          } else {
              Print("[DEBUG] ", buyOrderTypeName, " order placed. Ticket: ", orderTicket);
          }
      } else {
          Print("[DEBUG] ", buyOrderTypeName, " order already exists for slot ", IntegerToString(t), ". No duplicate placed.");
      }
      // Always use STOP pending orders at exact range boundaries (no LIMITs)
      int sellOrderType = OP_SELLSTOP;
      string sellOrderTypeName = "SELL STOP";
      string sellCommentFull = (!priceInsideRange) ? "Range Broken SELL STOP" : "Normal Breakout SELL STOP";

      // Check for existing open sell position for this slot
      if(!allowSell || sellStop <= 0) {
          Print("[DEBUG] Sell trades not allowed (allowSell=", allowSell, ") or sellStop is 0 (sellStop=", DoubleToString(sellStop, Digits), ")");
      } else if(OpenPositionExists(sellOrderType, sellMagic)) {
          Print("[DEBUG] Open SELL position exists for slot ", IntegerToString(t), ". Not placing new pending sell order.");
      } else if(!PendingOrderExists(sellOrderType, sellMagic, sellStopLevel, calculatedSellSL, sellTP, useLot)) {
          Print("[DEBUG] Sending ", sellOrderTypeName, " order for slot ", IntegerToString(t), " at price ", DoubleToString(sellStopLevel,Digits));
          int ticket = OrderSend(Symbol(), sellOrderType, useLot, sellStopLevel, 3, calculatedSellSL, sellTP, sellCommentFull+" "+IntegerToString(t)+" ("+sellComment+")", sellMagic, 0, sellColor);
          if(ticket < 0) {
              int err = GetLastError();
              Print("[ERROR] Failed to place ", sellOrderTypeName, " order. Error code: ", err);
          } else {
              Print("[DEBUG] ", sellOrderTypeName, " order placed. Ticket: ", ticket);
          }
      } else {
          Print("[DEBUG] ", sellOrderTypeName, " order already exists for slot ", IntegerToString(t), ". No duplicate placed.");
      }
   }

   // FINAL SAFETY: Always set the flag at the end to prevent any future calls today
   SetBreakoutTradesPlacedToday(true);
   Print("[PROTECTION] PlacePendingOrders() completed. BreakoutTradesPlacedToday set to true.");
}

//+------------------------------------------------------------------+
//| Place Fakeout Trade at Range Boundary                           |
//+------------------------------------------------------------------+
void PlaceFakeoutTrade(bool isBuy) {
   double useLot = ActualLotSize1;
   if(useLot <= 0) {
      double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
      if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
         useLot = CalcInitialLot(stopDist);
      else
         useLot = MarketInfo(Symbol(), MODE_MINLOT);
   }

   double rangeSize = MathAbs(RangeHigh - RangeLow);
   int fakeoutMagic = isBuy ? (MagicBase + 5000) : (MagicBase + 6000);
   string comment = isBuy ? "Fakeout Buy at Range Low" : "Fakeout Sell at Range High";

   // Check if fakeout trade already exists
   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == fakeoutMagic) {
            Print("[DEBUG] Fakeout trade already exists for ", (isBuy ? "BUY" : "SELL"));
            return;
         }
      }
   }

   if(isBuy) {
      // Place BUY at range low (expecting bounce back up)
      double buyPrice = Ask;
      double buySL = RangeLow - (AddedStopLoss * Point);
      double buyTP = RangeHigh; // Target is range high

      if(TradeTargetR > 0) buyTP = RangeLow + TradeTargetR * rangeSize;

      if(RobustOrderSend(Symbol(), OP_BUY, useLot, buyPrice, 3, buySL, buyTP, comment, fakeoutMagic, 0, clrGreen)) {
         Print("[FAKEOUT] BUY trade placed at range low. Price: ", DoubleToString(buyPrice, Digits));
      }
   } else {
      // Place SELL at range high (expecting bounce back down)
      double sellPrice = Bid;
      double sellSL = RangeHigh + (AddedStopLoss * Point);
      double sellTP = RangeLow; // Target is range low

      if(TradeTargetR > 0) sellTP = RangeHigh - TradeTargetR * rangeSize;

      if(RobustOrderSend(Symbol(), OP_SELL, useLot, sellPrice, 3, sellSL, sellTP, comment, fakeoutMagic, 0, clrRed)) {
         Print("[FAKEOUT] SELL trade placed at range high. Price: ", DoubleToString(sellPrice, Digits));
      }
   }
}

// Broker server time is used exclusively throughout the EA.

// Purge stale EA pendings from previous days during 00:00–06:00 server time
void PurgeStalePendingsForToday() {
   datetime today0 = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   for(int i = OrdersTotal()-1; i >= 0; i--) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) {
         int type = OrderType();
         if((type == OP_BUYSTOP || type == OP_SELLSTOP) && OrderOpenTime() < today0) {
            int ticket = OrderTicket();
            bool del = OrderDelete(ticket);
            if(del) Print("[CLEANUP] Deleted stale pending order ", ticket);
            else Print("[CLEANUP] Failed to delete stale pending order ", ticket, " Error: ", GetLastError());
         }
      }
   }
}


// --- Add stubs for missing functions so the file compiles ---
//+------------------------------------------------------------------+
//| Update Dashboard Display                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel() {
   if(!EnableDashboard && !EnableStatsPanel) return;

   // Delete existing dashboard objects
   DeleteDashboard();

   // Reset Y position
   DashboardCurrentY = DashboardYOffset;

   if(EnableDashboard) {
      CreateDashboard();
   } else if(EnableStatsPanel) {
      CreateSimpleStatsPanel();
   }
}

//+------------------------------------------------------------------+
//| Create Comprehensive Dashboard                                   |
//+------------------------------------------------------------------+
void CreateDashboard() {
   string prefix = DashboardPrefix;
   int x = DashboardXOffset;
   int y = DashboardCurrentY;

   // Dashboard title
   CreateDashboardLabel(prefix + "Title", "INNOVATIONX LIVE TRADING", x, y, DashboardTextColor, DashboardFontSize + 2, true);
   y += DashboardLineHeight + 5;

   // Account Balance & Equity
   CreateDashboardLabel(prefix + "Balance", "Balance: " + DoubleToString(AccountBalance(), 2), x, y, DashboardTextColor);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "Equity", "Equity: " + DoubleToString(AccountEquity(), 2), x, y, DashboardTextColor);
   y += DashboardLineHeight + 3;

   // Count live trades, pending orders, and calculate live P/L
   int liveTrades = 0, pendingOrders = 0, buyTrades = 0, sellTrades = 0;
   double liveProfit = 0, biggestWin = 0, biggestLoss = 0;
   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 100000) {
            if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
               liveTrades++;
               double tradeProfit = OrderProfit() + OrderSwap() + OrderCommission();
               liveProfit += tradeProfit;
               if(OrderType() == OP_BUY) buyTrades++;
               else sellTrades++;
               if(tradeProfit > biggestWin) biggestWin = tradeProfit;
               if(tradeProfit < biggestLoss) biggestLoss = tradeProfit;
            } else {
               pendingOrders++;
            }
         }
      }
   }

   // === ACCOUNT STATUS ===
   CreateDashboardLabel(prefix + "AccTitle", "▼ ACCOUNT STATUS", x, y, clrCyan, DashboardFontSize + 1, true);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "Balance", "Balance: $" + DoubleToString(AccountBalance(), 2), x, y, DashboardTextColor);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "Equity", "Equity: $" + DoubleToString(AccountEquity(), 2), x, y, DashboardTextColor);
   y += DashboardLineHeight;

   // Equity-based drawdown today/week
   double eq = AccountEquity();
   double dayLossAmt = DayStartEquity - eq;
   double dayLossPct = (DayStartEquity > 0) ? (dayLossAmt / DayStartEquity) * 100.0 : 0;
   double weekLossAmt = WeekStartEquity - eq;
   double weekLossPct = (WeekStartEquity > 0) ? (weekLossAmt / WeekStartEquity) * 100.0 : 0;
   CreateDashboardLabel(prefix + "DayDD", "Today DD (Equity): $" + DoubleToString(dayLossAmt,2) + " (" + DoubleToString(dayLossPct,2) + "%)", x, y, (dayLossAmt>0?DashboardLossColor:DashboardTextColor));
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "WeekDD", "Week DD (Equity): $" + DoubleToString(weekLossAmt,2) + " (" + DoubleToString(weekLossPct,2) + "%)", x, y, (weekLossAmt>0?DashboardLossColor:DashboardTextColor));
   y += DashboardLineHeight + 3;

   // Margin info
   double freeMargin = AccountFreeMargin();
   double marginLevel = (AccountMargin() > 0) ? AccountEquity() / AccountMargin() * 100 : 0;
   CreateDashboardLabel(prefix + "Margin", "Free Margin: $" + DoubleToString(freeMargin, 2), x, y, DashboardTextColor);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "MarginLevel", "Margin Level: " + DoubleToString(marginLevel, 1) + "%", x, y, DashboardTextColor);
   y += DashboardLineHeight + 3;

   // === LIVE POSITIONS ===
   CreateDashboardLabel(prefix + "LiveTitle", "▼ LIVE POSITIONS", x, y, clrYellow, DashboardFontSize + 1, true);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "LiveTrades", "Total: " + IntegerToString(liveTrades) + " | BUY: " + IntegerToString(buyTrades) + " | SELL: " + IntegerToString(sellTrades), x, y, DashboardTextColor);
   y += DashboardLineHeight;
   CreateDashboardLabel(prefix + "PendingOrders", "Pending Orders: " + IntegerToString(pendingOrders), x, y, DashboardTextColor);
   y += DashboardLineHeight;

   // Live P/L with color coding
   color plColor = (liveProfit >= 0) ? DashboardProfitColor : DashboardLossColor;
   double livePLPercent = (AccountBalance() > 0) ? (liveProfit / AccountBalance()) * 100.0 : 0;
   string plStatus = (liveProfit >= 0) ? "PROFIT" : "LOSS";
   CreateDashboardLabel(prefix + "LivePL", "Live " + plStatus + ": $" + DoubleToString(liveProfit, 2) + " (" + DoubleToString(livePLPercent, 2) + "%)", x, y, plColor);
   y += DashboardLineHeight;

   // Biggest win/loss if trades are open
   if(liveTrades > 0) {
      CreateDashboardLabel(prefix + "BigWin", "Best: $" + DoubleToString(biggestWin, 2), x, y, DashboardProfitColor);
      y += DashboardLineHeight;
      CreateDashboardLabel(prefix + "BigLoss", "Worst: $" + DoubleToString(biggestLoss, 2), x, y, DashboardLossColor);
      y += DashboardLineHeight;
   }
   y += 3;

   // === PERFORMANCE ===
   CreateDashboardLabel(prefix + "PerfTitle", "▼ PERFORMANCE", x, y, clrOrange, DashboardFontSize + 1, true);
   y += DashboardLineHeight;

   // Daily P/L
   color dayColor = (DailyProfitAmount >= 0) ? DashboardProfitColor : DashboardLossColor;
   string dayStatus = (DailyProfitAmount >= 0) ? "PROFIT" : "LOSS";
   CreateDashboardLabel(prefix + "DayPL", "Today: $" + DoubleToString(DailyProfitAmount, 2) + " (" + DoubleToString(DailyProfitPercent, 2) + "%)", x, y, dayColor);
   y += DashboardLineHeight;

   // Weekly P/L
   color weekColor = (WeeklyProfitAmount >= 0) ? DashboardProfitColor : DashboardLossColor;
   CreateDashboardLabel(prefix + "WeekPL", "Week: $" + DoubleToString(WeeklyProfitAmount, 2) + " (" + DoubleToString(WeeklyProfitPercent, 2) + "%)", x, y, weekColor);
   y += DashboardLineHeight;

   // Win Rate & Stats
   int totalTrades = DailyWins + DailyLosses;
   double winRate = (totalTrades > 0) ? (double)DailyWins / totalTrades * 100.0 : 0;
   color winColor = (winRate >= 50) ? DashboardProfitColor : DashboardLossColor;
   CreateDashboardLabel(prefix + "WinRate", "Win Rate: " + DoubleToString(winRate, 1) + "% (" + IntegerToString(DailyWins) + "W/" + IntegerToString(DailyLosses) + "L)", x, y, winColor);
   y += DashboardLineHeight;

   // Drawdown
   CreateDashboardLabel(prefix + "Drawdown", "Max DD: " + DoubleToString(MaxDrawdownPercent, 2) + "%", x, y, DashboardLossColor);
   y += DashboardLineHeight + 3;

   // === MARKET CONDITIONS ===
   CreateDashboardLabel(prefix + "MarketTitle", "▼ MARKET CONDITIONS", x, y, clrLightBlue, DashboardFontSize + 1, true);
   y += DashboardLineHeight;

   // Current spread
   double currentSpread = MarketInfo(Symbol(), MODE_SPREAD);
   color spreadColor = (currentSpread <= MaxSpreadPoints) ? DashboardTextColor : DashboardLossColor;
   CreateDashboardLabel(prefix + "Spread", "Spread: " + DoubleToString(currentSpread, 1) + " pts", x, y, spreadColor);
   y += DashboardLineHeight;

   // Range info if identified
   if(RangeIdentified) {
      double rangeSize = (RangeHigh - RangeLow) / Point;
      string rangeStatus = BreakoutTradesPlacedToday ? "TRADES PLACED" : "WAITING";
      color rangeColor = BreakoutTradesPlacedToday ? DashboardProfitColor : DashboardTextColor;
      CreateDashboardLabel(prefix + "Range", "Range: " + DoubleToString(rangeSize, 0) + " pts | " + rangeStatus, x, y, rangeColor);
      y += DashboardLineHeight;
   }

   // Server time
   string serverTime = TimeToString(TimeCurrent(), TIME_MINUTES);
   CreateDashboardLabel(prefix + "ServerTime", "Server: " + serverTime, x, y, DashboardTextColor);
   y += DashboardLineHeight + 3;

   // Create background rectangle
   CreateDashboardBackground(x - 5, DashboardYOffset - 5, 280, y - DashboardYOffset + 10);
}

//+------------------------------------------------------------------+
//| Create Simple Stats Panel (Legacy)                              |
//+------------------------------------------------------------------+
void CreateSimpleStatsPanel() {
   string info = "Range Breakout EA\n";
   info += "Balance: " + DoubleToString(AccountBalance(), 2) + "\n";
   info += "Equity: " + DoubleToString(AccountEquity(), 2) + "\n";
   info += "Daily P/L: " + DoubleToString(DailyProfitAmount, 2) + " (" + DoubleToString(DailyProfitPercent, 2) + "%)\n";
   if(RangeIdentified) {
      info += "Range: " + DoubleToString(RangeLow, Digits) + " - " + DoubleToString(RangeHigh, Digits) + "\n";
   }
   Comment(info);
}

void DeletePendingOrders() {
   for(int i = OrdersTotal() - 1; i >= 0; i--) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 100) {
            if(OrderType() == OP_BUYSTOP || OrderType() == OP_SELLSTOP || OrderType() == OP_BUYLIMIT || OrderType() == OP_SELLLIMIT) {
               bool result = OrderDelete(OrderTicket());
               if(result) {
                  Print("[DELETE] Pending order ", OrderTicket(), " deleted successfully.");
               } else {
                  Print("[ERROR] Failed to delete pending order ", OrderTicket(), ". Error: ", GetLastError());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manual Reset Function (for advanced users)                      |
//+------------------------------------------------------------------+
void ManualResetTradingStop() {
   // This function can be called to manually reset the trading stop
   // Advanced users can add a button or hotkey to call this function
   TradingPermanentlyStopped = false;
   TradingStopReason = "";

   // Clear global variables
   string stopGlobalVarName = "TradingStop_" + Symbol() + "_" + IntegerToString(MagicBase);
   GlobalVariableDel(stopGlobalVarName);
   GlobalVariableDel(stopGlobalVarName + "_Reason");

   // Reset loss limit flags
   DayLossLimitHit = false;
   WeekLossLimitHit = false;
   TotalLossLimitHit = false;
   EmergencyStopHit = false;

   // Reset equity tracking to current values
   DayStartEquity = AccountEquity();
   WeekStartEquity = AccountEquity();
   SessionStartEquity = AccountEquity();

   Alert("Trading stop has been manually reset. EA will resume normal operations.");
   Print("[MANUAL RESET] Trading stop cleared. EA resumed.");
   Comment(""); // Clear warning message
}

//+------------------------------------------------------------------+
//| Dashboard Helper Functions                                       |
//+------------------------------------------------------------------+
void CreateDashboardLabel(string name, string text, int x, int y, color clr, int fontSize = 0, bool bold = false) {
   if(fontSize == 0) fontSize = DashboardFontSize;

   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_CORNER, DashboardCorner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, DashboardFont);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontSize);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

void CreateDashboardBackground(int x, int y, int width, int height) {
   string name = DashboardPrefix + "Background";

   // Delete existing background first to prevent conflicts
   ObjectDelete(0, name);

   // Create new background
   ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_CORNER, DashboardCorner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, DashboardBackColor);
   ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrGray);
   ObjectSetInteger(0, name, OBJPROP_BACK, true);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

void DeleteDashboard() {
   // Delete all dashboard objects
   for(int i = ObjectsTotal() - 1; i >= 0; i--) {
      string objName = ObjectName(i);
      if(StringFind(objName, DashboardPrefix) == 0) {
         ObjectDelete(0, objName);
      }
   }
}

//+------------------------------------------------------------------+
//| Close All Positions Function                                     |
//+------------------------------------------------------------------+
void CloseAllPositions() {
   for(int i = OrdersTotal() - 1; i >= 0; i--) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 100) {
            if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
               double closePrice = (OrderType() == OP_BUY) ? Bid : Ask;
               bool result = OrderClose(OrderTicket(), OrderLots(), closePrice, 3);
               if(result) {
                  Print("[CLOSE] Position ", OrderTicket(), " closed due to risk limit.");
               } else {
                  Print("[ERROR] Failed to close position ", OrderTicket(), ". Error: ", GetLastError());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update Trade Statistics                                          |
//+------------------------------------------------------------------+
void UpdateTradeStatistics() {
   int totalTrades = 0;
   int winningTrades = 0;
   double totalProfit = 0;
   double totalWinProfit = 0;
   double totalLossProfit = 0;

   // Count closed trades for this EA
   for(int i = 0; i < OrdersHistoryTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 100) {
            if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
               totalTrades++;
               totalProfit += OrderProfit() + OrderSwap() + OrderCommission();

               if(OrderProfit() > 0) {
                  winningTrades++;
                  totalWinProfit += OrderProfit() + OrderSwap() + OrderCommission();
               } else {
                  totalLossProfit += OrderProfit() + OrderSwap() + OrderCommission();
               }
            }
         }
      }
   }

   // Calculate statistics
   if(totalTrades > 0) {
      PerfWinRate = (double)winningTrades / totalTrades * 100.0;
      PerfAvgWin = (winningTrades > 0) ? totalWinProfit / winningTrades : 0;
      PerfAvgLoss = (totalTrades - winningTrades > 0) ? totalLossProfit / (totalTrades - winningTrades) : 0;

      if(PerfAvgLoss != 0) {
         PerfExpectancy = (PerfWinRate / 100.0 * PerfAvgWin) + ((1 - PerfWinRate / 100.0) * PerfAvgLoss);
      }
   }
}

//+------------------------------------------------------------------+
//| Track Trade Outcome for Statistics                              |
//+------------------------------------------------------------------+
void TrackTradeOutcome(double profit) {
   datetime now = TimeCurrent();
   int currentDay = TimeDayOfYear(now);
   int currentWeek = currentDay / 7;
   int currentMonth = TimeMonth(now);

   // Update daily stats
   if(currentDay == LastDay) {
      if(profit > 0) {
         DailyWins++;
      } else {
         DailyLosses++;
      }
   }

   // Update weekly stats
   if(currentWeek == LastWeek) {
      if(profit > 0) {
         WeeklyWins++;
      } else {
         WeeklyLosses++;
      }
   }

   // Update monthly stats
   if(currentMonth == LastMonth) {
      if(profit > 0) {
         MonthlyWins++;
      } else {
         MonthlyLosses++;
      }
   }

   Print("[STATS] Trade outcome tracked: Profit=", DoubleToString(profit, 2),
         ", Daily W/L: ", DailyWins, "/", DailyLosses,
         ", Weekly W/L: ", WeeklyWins, "/", WeeklyLosses,
         ", Monthly W/L: ", MonthlyWins, "/", MonthlyLosses);
}

//+------------------------------------------------------------------+
//| Monitor for Closed Trades                                       |
//+------------------------------------------------------------------+
void MonitorClosedTrades() {
   static int lastHistoryTotal = 0;
   int currentHistoryTotal = OrdersHistoryTotal();

   // Check if new trades were closed
   if(currentHistoryTotal > lastHistoryTotal) {
      // Check the most recent closed trades
      for(int i = lastHistoryTotal; i < currentHistoryTotal; i++) {
         if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 100) {
               if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
                  double totalProfit = OrderProfit() + OrderSwap() + OrderCommission();
                  TrackTradeOutcome(totalProfit);
               }
            }
         }
      }
      lastHistoryTotal = currentHistoryTotal;
   }
}

bool RobustOrderSend(string symbol, int op, double lot, double price, int slippage, double sl, double tp, string comment, int magic, int expiry, color arrow_color) {
   // Final safety check before any order placement (active only when risk management is enabled)
   if(EnableRiskManagement) {
      // Only daily limit blocks new orders; weekly/total are informational for now
      if(DayLossLimitHit) {
         Print("[CRITICAL] RobustOrderSend blocked: Daily loss limit hit (", DoubleToString(MaxDailyLossPercent, 1), "%)");
         return false;
      }
   }

   int ticket = OrderSend(symbol, op, lot, price, slippage, sl, tp, comment, magic, expiry, arrow_color);
   if(ticket < 0) {
      int err = GetLastError();
      Print("[ERROR] RobustOrderSend failed. Error code: ", err, " for ", comment);
      return false;
   }
   Print("[SUCCESS] RobustOrderSend placed order. Ticket: ", ticket, " for ", comment);
   return true;
}

// Run at 22:50 server time to perform end-of-day cleanup on the broker clock
void ManageEndOfDayServer() {
   Print("[EOD] 22:50 Server: Closing all positions and deleting pending orders for ", Symbol());
   // Close any open positions for this EA/symbol
   CloseAllPositions();
   // Delete any remaining pending orders set by this EA/symbol
   DeletePendingOrders();
   // IMPORTANT: Do not reset BreakoutTradesPlacedToday here; it resets at server midnight
}
void ManageEndOfDayPositions() { /* ...implement as needed... */ }

// --- Legacy Berlin-time function removed; server time is used exclusively ---
/* Removed: ManageEndOfDayBerlin */

    for (int i = OrdersTotal() - 1; i >= 0; i--) {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if (OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) {
                int type = OrderType();
                int localTicket = OrderTicket();

                // DAY TRADING BOT: Close ALL open positions (both winning and losing)
                if (type == OP_BUY || type == OP_SELL) {
                    double closePrice = (type == OP_BUY) ? Bid : Ask;
                    double profit = OrderProfit() + OrderSwap() + OrderCommission();

                    bool closed = OrderClose(localTicket, OrderLots(), closePrice, 3, clrOrange);
                    if (closed) {
                        closedTrades++;
                        string profitStatus = (profit >= 0) ? "PROFIT" : "LOSS";
                        Print("[EOD] Closed ", profitStatus, " trade ticket ", localTicket, " | P/L: ", DoubleToString(profit, 2));
                    } else {
                        Print("[EOD] Failed to close trade ticket ", localTicket, " Error: ", GetLastError());
                    }
                }

                // Delete all pending orders
                if (type == OP_BUYSTOP || type == OP_SELLSTOP || type == OP_BUYLIMIT || type == OP_SELLLIMIT) {
                    bool deleted = OrderDelete(localTicket);
                    if (deleted) {
                        deletedPending++;
                        Print("[EOD] Deleted pending order ticket ", localTicket);
                    } else {
                        Print("[EOD] Failed to delete pending order ticket ", localTicket, " Error: ", GetLastError());
                    }
                }
            }
        }
    }

// Ensure only one `ManagePositions` function exists
void ManagePositions() {
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) {
                int type = OrderType();
                double openPrice = OrderOpenPrice();
                double lots = OrderLots();
                int ticket = OrderTicket();
                double profit = OrderProfit();
                double curSL = OrderStopLoss();
                double curTP = OrderTakeProfit();

                // --- Breakeven Logic ---
                if(UseBreakEvenProfitAmount && BreakEvenProfitAmount > 0) {

                    if(profit >= BreakEvenProfitAmount && (type == OP_BUY || type == OP_SELL)) {
                        // Only move SL to BE if not already at BE
                        if((type == OP_BUY && (curSL < openPrice || curSL == 0)) ||
                           (type == OP_SELL && (curSL > openPrice || curSL == 0))) {
                            bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                            if(mod)
                                Print("[INFO] Breakeven (profit): SL moved to BE. Ticket ", ticket);
                            else
                                Print("[ERROR] Breakeven (profit): Failed to move SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                } else if(UseBreakEven && (type == OP_BUY || type == OP_SELL)) {
                    double beTrigger = 0;
                    if(UseBreakEvenPercent && BreakEvenPercent >  0) {
                        beTrigger = openPrice * (BreakEvenPercent / 100.0);
                    } else if(BreakEvenPoints > 0) {
                        beTrigger = BreakEvenPoints * Point;
                    } else if(BETriggerPoints > 0) {
                        beTrigger = BETriggerPoints * Point;
                    }
                    // For BUY
                    if(type == OP_BUY && Bid - openPrice >= beTrigger && (curSL < openPrice || curSL == 0)) {
                        bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                        if(mod)
                            Print("[INFO] Breakeven: BUY SL moved to BE. Ticket ", ticket);
                        else
                            Print("[ERROR] Breakeven: Failed to move BUY SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                    }
                    // For SELL
                    if(type == OP_SELL && openPrice - Ask >= beTrigger && (curSL > openPrice || curSL == 0)) {
                        bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                        if(mod)
                            Print("[INFO] Breakeven: SELL SL moved to BE. Ticket ", ticket);
                        else
                            Print("[ERROR] Breakeven: Failed to move SELL SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                    }
                }

                // --- Trailing Stop Logic ---
                // If UseTrailingProfitAmount is true, trail by profit amount; else, trail by points/percent
                if(UseTrailingProfitAmount && TrailingProfitTrigger > 0 && TrailingProfitStep > 0 && (type == OP_BUY || type == OP_SELL)) {
                    if(profit >= TrailingProfitTrigger) {
                        double newSL = 0;
                        if(type == OP_BUY) {
                            newSL = openPrice + ((profit - TrailingProfitStep) / lots) / MarketInfo(Symbol(), MODE_TICKVALUE) * Point;
                            if(Bid > newSL && (curSL < newSL || curSL == 0)) {
                                bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                                if(mod)
                                    Print("[INFO] TrailingStop (profit): BUY SL trailed. Ticket ", ticket);
                                else
                                    Print("[ERROR] TrailingStop (profit): Failed to trail BUY SL. Ticket ", ticket, " Error: ", GetLastError());
                            }
                        } else if(type == OP_SELL) {
                            newSL = openPrice - ((profit - TrailingProfitStep) / lots) / MarketInfo(Symbol(), MODE_TICKVALUE) * Point;
                            if(Ask < newSL && (curSL > newSL || curSL == 0)) {
                                bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                                if(mod)
                                    Print("[INFO] TrailingStop (profit): SELL SL trailed. Ticket ", ticket);
                                else
                                    Print("[ERROR] TrailingStop (profit): Failed to trail SELL SL. Ticket ", ticket, " Error: ", GetLastError());
                            }
                        }
                    }
                } else if((UseTrailingStop || ScalpingMode) && (type == OP_BUY || type == OP_SELL)) {
                    double trailDist = 0;
                    if(UseTrailingStopPercent && TrailingStopPercent > 0) {
                        trailDist = openPrice * (TrailingStopPercent / 100.0);
                    } else if(TrailingStopPoints > 0) {
                        trailDist = TrailingStopPoints * Point;
                    }
                    // For BUY - scalping mode trails immediately, normal mode waits for profit
                    if(type == OP_BUY && trailDist > 0) {
                        double newSL = Bid - trailDist;
                        bool shouldTrail = false;

                        if(ScalpingMode) {
                            // SCALPING: Immediate 25% risk reduction + aggressive trailing
                            double currentProfit = Bid - openPrice;
                            double hawkModeProfit = 30 * Point; // 3 pips = hawk mode

                            Print("[SCALPING DEBUG] BUY Trade ", ticket, ": Current profit = ", DoubleToString(currentProfit/Point, 1), " pips, Hawk threshold = ", DoubleToString(hawkModeProfit/Point, 1), " pips");

                            // STAGE 1: IMMEDIATE 25% RISK REDUCTION - as soon as trade is active
                            double originalRisk = openPrice - curSL; // Original SL distance
                            if(originalRisk <= 0 || curSL == 0) {
                                // Fallback: assume range-based SL (typical 50-100 pips)
                                originalRisk = MathAbs(RangeHigh - RangeLow);
                                if(originalRisk <= 0) originalRisk = 50 * Point; // Final fallback
                            }
                            double reducedSL = openPrice - (originalRisk * 0.75); // Cut loss by 25%

                            if(currentProfit >= hawkModeProfit) {
                                // STAGE 2: HAWK MODE - super aggressive, right behind price
                                double hawkSL = Bid - 10 * Point; // 1 pip behind price
                                shouldTrail = (hawkSL > curSL + Point);
                                if(shouldTrail) {
                                    newSL = hawkSL; // Override with hawk SL
                                    Print("[SCALPING] HAWK MODE: Ultra-aggressive trailing at ", DoubleToString(newSL, Digits), " (1 pip behind price)");
                                }
                            } else if(reducedSL > curSL || curSL == 0) {
                                // Apply 25% risk reduction immediately when trade becomes active
                                shouldTrail = true;
                                newSL = reducedSL;
                                Print("[SCALPING] IMMEDIATE Risk Reduction: Cut loss by 25% as soon as trade active. Original risk: ", DoubleToString(originalRisk/Point, 0), " pips, New SL: ", DoubleToString(newSL, Digits));
                            } else if(currentProfit > 0) {
                                // STAGE 1.5: Normal trailing after risk reduction already applied
                                shouldTrail = (newSL > curSL + Point);
                                if(shouldTrail) {
                                    Print("[SCALPING] Normal trailing: Moving SL to ", DoubleToString(newSL, Digits));
                                }
                            }
                        } else {
                            // NORMAL: Give price room to breathe - only trail when well in profit
                            double minProfitToTrail = trailDist * 2; // Need 2x trail distance in profit before trailing
                            shouldTrail = (Bid - openPrice > minProfitToTrail && newSL > curSL + Point);
                        }

                        if(shouldTrail) {
                           bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                           if(mod)
                               Print(ScalpingMode ? "[SCALPING]" : "[NORMAL]", " BUY SL trailed. Ticket ", ticket, " New SL: ", DoubleToString(newSL, Digits));
                           else
                               Print("[ERROR] TrailingStop: Failed to trail BUY SL. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                    // For SELL - scalping mode trails immediately, normal mode waits for profit
                    if(type == OP_SELL && trailDist > 0) {
                        double newSL = Ask + trailDist;
                        bool shouldTrail = false;

                        if(ScalpingMode) {
                            // SCALPING: Immediate 25% risk reduction + aggressive trailing
                            double currentProfit = openPrice - Ask;
                            double hawkModeProfit = 30 * Point; // 3 pips = hawk mode

                            Print("[SCALPING DEBUG] SELL Trade ", ticket, ": Current profit = ", DoubleToString(currentProfit/Point, 1), " pips, Hawk threshold = ", DoubleToString(hawkModeProfit/Point, 1), " pips");

                            // STAGE 1: IMMEDIATE 25% RISK REDUCTION - as soon as trade is active
                            double originalRisk = curSL - openPrice; // Original SL distance
                            if(originalRisk <= 0 || curSL == 0) {
                                // Fallback: assume range-based SL (typical 50-100 pips)
                                originalRisk = MathAbs(RangeHigh - RangeLow);
                                if(originalRisk <= 0) originalRisk = 50 * Point; // Final fallback
                            }
                            double reducedSL = openPrice + (originalRisk * 0.75); // Cut loss by 25%

                            if(currentProfit >= hawkModeProfit) {
                                // STAGE 2: HAWK MODE - super aggressive, right behind price
                                double hawkSL = Ask + 10 * Point; // 1 pip behind price
                                shouldTrail = (hawkSL < curSL - Point || curSL == 0);
                                if(shouldTrail) {
                                    newSL = hawkSL; // Override with hawk SL
                                    Print("[SCALPING] HAWK MODE: Ultra-aggressive trailing at ", DoubleToString(newSL, Digits), " (1 pip behind price)");
                                }
                            } else if(reducedSL < curSL || curSL == 0) {
                                // Apply 25% risk reduction immediately when trade becomes active
                                shouldTrail = true;
                                newSL = reducedSL;
                                Print("[SCALPING] IMMEDIATE Risk Reduction: Cut loss by 25% as soon as trade active. Original risk: ", DoubleToString(originalRisk/Point, 0), " pips, New SL: ", DoubleToString(newSL, Digits));
                            } else if(currentProfit > 0) {
                                // STAGE 1.5: Normal trailing after risk reduction already applied
                                shouldTrail = (newSL < curSL - Point || curSL == 0);
                                if(shouldTrail) {
                                    Print("[SCALPING] Normal trailing: Moving SL to ", DoubleToString(newSL, Digits));
                                }
                            }
                        } else {
                           // NORMAL: Give price room to breathe - only trail when well in profit
                           double minProfitToTrail = trailDist * 2; // Need 2x trail distance in profit before trailing
                           shouldTrail = (openPrice - Ask > minProfitToTrail && (newSL < curSL - Point || curSL == 0));
                        }

                        if(shouldTrail) {
                           bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                           if(mod)
                               Print(ScalpingMode ? "[SCALPING]" : "[NORMAL]", " SELL SL trailed. Ticket ", ticket, " New SL: ", DoubleToString(newSL, Digits));
                           else
                               Print("[ERROR] TrailingStop: Failed to trail SELL SL. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                }
            }
        }
    }
}

// --- Add this helper function to get asset type (0=Forex, 1=Indices/Metals/Crypto) ---
int GetAssetType() {
    string sym = Symbol();
    // Manual uppercase conversion (since StringToUpper doesn't exist in MQL4)
    string symUpper = "";
    for(int i = 0; i < StringLen(sym); i++) {
      ushort ch = (ushort)StringGetCharacter(sym, i);
      if(ch >= 97 && ch <= 122) ch = ch - 32; // Convert lowercase to uppercase
      string charStr = CharToString((char)ch);
      StringConcatenate(symUpper, symUpper, charStr);
   }
    // Check for indices, metals, and crypto symbols
    if(StringFind(symUpper, "NAS") >= 0 || StringFind(symUpper, "SPX") >= 0 || StringFind(symUpper, "DAX") >= 0 ||
       StringFind(symUpper, "DJ") >= 0 || StringFind(symUpper, "UK") >= 0 || StringFind(symUpper, "HK") >= 0 ||
       StringFind(symUpper, "BTC") >= 0 || StringFind(symUpper, "ETH") >= 0 || StringFind(symUpper, "LTC") >= 0 ||
       StringFind(symUpper, "XAU") >= 0 || StringFind(symUpper, "XAG") >= 0 || StringFind(symUpper, "GOLD") >= 0 || StringFind(symUpper, "SILVER") >= 0)
        return 1; // Indices/Metals/Crypto
    return 0; // Forex
}
