```mql4
//+------------------------------------------------------------------+
//|                                             TrailingStopLogic.mq4 |
//|                        Copyright 2023, MetaQuotes Software Corp. |
//|                                       https://www.metaquotes.net/ |
//+------------------------------------------------------------------+
#property strict

input double breakevenPoints = 300; // Breakeven at 300 points
input double trailingStopPoints = 1000; // Trailing stop activates at 1000 points

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   //--- create timer
   EventSetTimer(1);
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   //--- destroy timer
   EventKillTimer();
  }
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
   //--- loop through all orders
   for(int i = OrdersTotal() - 1; i >= 0; i--)
     {
      if(OrderSelect(i, SELECT_BY_POS))
        {
         //--- manage trade for the selected order
         ManageTrade(OrderTicket());
        }
     }
  }
//+------------------------------------------------------------------+
//| Manage trade function                                            |
//+------------------------------------------------------------------+
void ManageTrade(int ticket)
  {
   double entryPrice = OrderOpenPrice();
   double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask; // Use Bid for buy orders, Ask for sell orders
   double stopLoss = OrderStopLoss();
   double pointValue = Point;

   // Calculate profit in points
   double profitPoints = (OrderType() == OP_BUY) 
                         ? (currentPrice - entryPrice) / pointValue 
                         : (entryPrice - currentPrice) / pointValue;

   // Breakeven logic
   if(profitPoints >= breakevenPoints && stopLoss < entryPrice)
     {
      // Move stop-loss to breakeven
      ModifyOrder(ticket, entryPrice, OrderTakeProfit());
     }

   // Trailing stop logic
   if(profitPoints >= trailingStopPoints)
     {
      double newStopLoss = (OrderType() == OP_BUY) 
                           ? currentPrice - (trailingStopPoints * pointValue) 
                           : currentPrice + (trailingStopPoints * pointValue);

      if((OrderType() == OP_BUY && newStopLoss > stopLoss) || 
         (OrderType() == OP_SELL && newStopLoss < stopLoss))
        {
         ModifyOrder(ticket, newStopLoss, OrderTakeProfit());
        }
     }
  }
//+------------------------------------------------------------------+
//| Modify order function                                            |
//+------------------------------------------------------------------+
bool ModifyOrder(int ticket, double newStopLoss, double takeProfit)
  {
   if(!OrderModify(ticket, OrderOpenPrice(), newStopLoss, takeProfit, 0, clrNONE))
     {
      Print("Failed to modify order #", ticket, " Error: ", GetLastError());
      return(false);
     }
   return(true);
  }
//+------------------------------------------------------------------+
```