# Innovationx Range Breakout EA - Dashboard & Risk Management Guide

## Overview
The EA now includes a comprehensive trading dashboard and advanced risk management system to help you monitor performance and protect your account.

## New Risk Management Parameters

### Risk Management & Loss Limits Section
- **EnableRiskManagement**: Enable/disable the risk management system (default: ON)
- **StartingBalanceOverride**: Manual starting balance (0 = auto-capture current balance)
- **MaxDailyLossPercent**: Maximum daily loss as percentage (default: 4% - ENABLED)
- **MaxDailyLossAmount**: Maximum daily loss in account currency (0 = use percent only)
- **MaxWeeklyLossPercent**: Maximum weekly loss as percentage (default: 8% - ENABLED)
- **MaxWeeklyLossAmount**: Maximum weekly loss in account currency (0 = use percent only)
- **MaxTotalLossPercent**: Maximum total loss from starting balance (default: 8% - ENABLED)
- **MaxTotalLossAmount**: Maximum total loss in account currency (0 = use percent only)
- **EmergencyStopEquityPercent**: Emergency stop when equity drops below this % (default: 80%)
- **CloseAllOnDailyLimit**: Close all trades when daily limit is hit
- **CloseAllOnWeeklyLimit**: Close all trades when weekly limit is hit
- **DeletePendingOnLimit**: Delete pending orders when loss limit is hit
- **ResetStartingBalanceDaily**: Reset starting balance daily (true) or keep original (false)

## Dashboard Display Settings

### Dashboard Configuration
- **EnableDashboard**: Show the comprehensive trading dashboard
- **DashboardCorner**: Position (0=top-left, 1=top-right, 2=bottom-left, 3=bottom-right)
- **DashboardXOffset**: Horizontal offset from corner
- **DashboardYOffset**: Vertical offset from corner
- **DashboardBackColor**: Background color (default: Black)
- **DashboardTextColor**: Text color (default: White)
- **DashboardProfitColor**: Color for profit values (default: Lime)
- **DashboardLossColor**: Color for loss values (default: Red)
- **DashboardFontSize**: Font size (default: 8)
- **DashboardFont**: Font name (default: Arial)

### Dashboard Content Options
- **ShowAccountInfo**: Display account balance and equity information
- **ShowDailyStats**: Display daily profit/loss statistics
- **ShowWeeklyStats**: Display weekly profit/loss statistics
- **ShowMonthlyStats**: Display monthly profit/loss statistics
- **ShowRiskLimits**: Display risk management limits and status
- **ShowTradeStats**: Display trade statistics (win rate, expectancy, etc.)
- **ShowRangeInfo**: Display current range information

## Dashboard Information Display

### Account Information
- Current Balance
- Current Equity
- Starting Balance
- Session Start Balance

### Daily Statistics
- Daily Profit/Loss (amount and percentage)
- Daily Wins vs Losses count
- Daily Win Rate percentage

### Weekly Statistics
- Weekly Profit/Loss (amount and percentage)
- Weekly Wins vs Losses count
- Weekly Win Rate percentage

### Monthly Statistics
- Monthly Profit/Loss (amount and percentage)
- Monthly Wins vs Losses count
- Monthly Win Rate percentage

### Risk Management Status
- Daily Loss Limit Status (OK/HIT)
- Weekly Loss Limit Status (OK/HIT)
- Total Loss Limit Status (OK/HIT)
- Maximum Drawdown percentage

### Range Information
- Current Range High
- Current Range Low
- Range Size
- Trade Placement Status (PLACED/PENDING)

### Trade Statistics
- Overall Win Rate
- Expectancy
- Average Win
- Average Loss

## Risk Management Features

### Automatic Loss Protection
1. **Daily Limits (4% default)**: When daily loss limit is reached:
   - All pending orders are deleted
   - All open positions are closed
   - No new trades until next day (automatic reset at 00:00 Berlin)

2. **Weekly Limits (8% default)**: When weekly loss limit is reached:
   - All pending orders are deleted
   - All open positions are closed
   - **TRADING PERMANENTLY STOPPED** - requires manual EA reset
   - EA will refuse to trade until removed and re-attached

3. **Total Limits (8% default)**: When total loss limit is reached:
   - All pending orders are deleted
   - All open positions are closed
   - **TRADING PERMANENTLY STOPPED** - requires manual EA reset
   - EA will refuse to trade until removed and re-attached

4. **Emergency Stop (80% equity)**: When equity drops below emergency threshold:
   - All trades are immediately closed
   - All pending orders are deleted
   - **TRADING PERMANENTLY STOPPED** - requires manual EA reset

### Performance Tracking
- Real-time monitoring of trade outcomes
- Automatic calculation of win rates and expectancy
- Drawdown tracking and maximum drawdown recording
- Daily, weekly, and monthly performance analysis

## Usage Instructions

### Setting Up the Dashboard
1. Enable the dashboard: Set `EnableDashboard = true`
2. Choose your preferred corner position with `DashboardCorner`
3. Adjust positioning with `DashboardXOffset` and `DashboardYOffset`
4. Customize colors and fonts to your preference
5. Select which information sections to display

### Configuring Risk Management
1. Risk management is enabled by default with safe limits
2. Default daily loss limit: `MaxDailyLossPercent = 4%` (already enabled)
3. Default weekly loss limit: `MaxWeeklyLossPercent = 8%` (already enabled)
4. Adjust limits if needed: (e.g., 2% daily for conservative, 6% for aggressive)
5. Default total loss limit: `MaxTotalLossPercent = 8%` (already enabled - same as weekly)
5. Configure automatic actions when limits are hit

### Setting Starting Balance
**Option 1: Auto-Capture (Default)**
- Set `StartingBalanceOverride = 0.0`
- EA automatically captures current account balance when first loaded
- Best for new users or when starting fresh

**Option 2: Manual Override**
- Set `StartingBalanceOverride = [your desired amount]` (e.g., 10000.0)
- EA uses your specified amount as the baseline for risk calculations
- Useful when you want to use safety features based on a specific balance
- Example: Account has $15,000 but you want 8% limit based on $10,000

**Option 3: Daily Reset**
- Set `ResetStartingBalanceDaily = true`
- Starting balance resets every day to current balance (or override amount)
- Useful for compound growth strategies
- Risk limits adjust daily to current account size

### Manual Reset Procedure (When Trading is Stopped)
When weekly/total loss limits are hit, trading is permanently stopped:
1. **Remove the EA** from the chart completely
2. **Re-attach the EA** with fresh settings
3. **Confirm** that the dashboard shows "✅ TRADING ACTIVE"
4. Trading will resume with current account balance as new baseline

## Starting Balance Examples

### Example 1: New Account (Auto-Capture)
```
Account Balance: $10,000
StartingBalanceOverride: 0.0 (default)
Result: EA uses $10,000 as baseline
Daily Limit: 4% = $400
Weekly/Total Limit: 8% = $800
```

### Example 2: Existing Account (Manual Override)
```
Account Balance: $15,000 (includes previous profits)
StartingBalanceOverride: 10000.0 (your original deposit)
Result: EA uses $10,000 as baseline for safety
Daily Limit: 4% = $400 (of $10,000)
Weekly/Total Limit: 8% = $800 (of $10,000)
Benefits: Protects original capital, allows profit to compound
```

### Example 3: Daily Compound Growth
```
Account Balance: $12,000
StartingBalanceOverride: 0.0
ResetStartingBalanceDaily: true
Result: Each day EA uses current balance as new baseline
Day 1: 4% of $12,000 = $480 daily limit
Day 2: 4% of current balance = adjusted daily limit
Benefits: Risk limits grow with account, maximizes profit potential
```

### Example 4: Conservative Fixed Baseline
```
Account Balance: $20,000
StartingBalanceOverride: 10000.0
ResetStartingBalanceDaily: false
Result: Always uses $10,000 as baseline regardless of growth
Daily Limit: Always 4% = $400
Weekly/Total Limit: Always 8% = $800
Benefits: Ultra-conservative, protects original capital absolutely
```
6. Set emergency stop level for account protection

### Monitoring Performance
- The dashboard updates in real-time during trading
- Monitor your daily progress throughout the session
- Track weekly and monthly performance trends
- Watch risk limit status to avoid hitting limits
- Use the statistics to optimize your trading parameters

## Best Practices

### Risk Management
- Default safe limits are pre-configured (4% daily, 8% weekly, 8% total)
- Risk management is enabled by default for account protection
- Automatic position closure and pending order deletion are enabled
- Monitor the dashboard regularly during trading hours
- Adjust limits based on your risk tolerance and account size

### Dashboard Usage
- Position the dashboard where it doesn't interfere with chart analysis
- Use contrasting colors for easy reading
- Enable all relevant information sections for comprehensive monitoring
- Check the range information to confirm trade placement status

### Performance Analysis
- Review daily statistics at the end of each trading session
- Analyze weekly performance to identify patterns
- Use monthly statistics for long-term strategy evaluation
- Monitor win rate and expectancy to assess strategy effectiveness

## Troubleshooting

### Dashboard Not Showing
- Ensure `EnableDashboard = true`
- Check that the corner position and offsets are appropriate for your screen
- Verify that the colors are not the same as your chart background

### Risk Limits Not Working
- Confirm `EnableRiskManagement = true`
- Check that loss percentages are set to reasonable values (> 0)
- Ensure the EA has sufficient permissions to close trades

### Statistics Not Updating
- Verify that trades are being placed with the correct magic numbers
- Check that the EA is running continuously during trading hours
- Ensure the trade monitoring functions are active

## Support
For additional support or questions about the dashboard and risk management features, contact the development team at the provided support channels.
